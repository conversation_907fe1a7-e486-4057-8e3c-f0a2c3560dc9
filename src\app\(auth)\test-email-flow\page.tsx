/**
 * 邮箱验证流程测试页面
 * 用于测试和演示完整的邮箱验证注册流程
 */

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ArrowRight, TestTube } from 'lucide-react'

export default function TestEmailFlowPage() {
  const router = useRouter()
  const [testEmail, setTestEmail] = useState('<EMAIL>')

  const testScenarios = [
    {
      title: '邮箱验证等待页面',
      description: '模拟邮件发送成功后的等待状态',
      path: `/email-verification-pending?email=${encodeURIComponent(testEmail)}`,
      color: 'bg-blue-50 border-blue-200'
    },
    {
      title: '邮件发送失败页面',
      description: '模拟邮件发送失败的情况',
      path: `/email-verification-failed?email=${encodeURIComponent(testEmail)}&error=${encodeURIComponent('邮件服务暂时不可用')}`,
      color: 'bg-red-50 border-red-200'
    },
    {
      title: '邮箱验证处理页面',
      description: '模拟处理邮件中的验证链接',
      path: `/verify-email?token=test-token-123`,
      color: 'bg-yellow-50 border-yellow-200'
    },
    {
      title: '验证成功页面',
      description: '模拟邮箱验证成功的状态',
      path: '/verify-success',
      color: 'bg-green-50 border-green-200'
    },
    {
      title: '验证失败页面',
      description: '模拟邮箱验证失败的情况',
      path: `/verify-failed?error=${encodeURIComponent('验证链接已过期')}`,
      color: 'bg-red-50 border-red-200'
    }
  ]

  const handleTestScenario = (path: string) => {
    router.push(path)
  }

  const handleBackToRegister = () => {
    router.push('/register')
  }

  return (
    <div className="min-h-svh flex flex-col items-center justify-center p-4 sm:p-6 md:p-10">
      <div className="w-full max-w-4xl">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
              <TestTube className="w-8 h-8 text-purple-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              邮箱验证流程测试
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              测试和演示完整的邮箱验证注册流程
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* 测试邮箱输入 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <Label htmlFor="testEmail" className="text-sm font-medium text-gray-700">
                测试邮箱地址
              </Label>
              <Input
                id="testEmail"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="输入测试邮箱地址"
                className="mt-2"
              />
              <p className="text-xs text-gray-500 mt-1">
                此邮箱地址将用于测试各个页面的显示效果
              </p>
            </div>

            {/* 测试场景列表 */}
            <div className="grid gap-4 md:grid-cols-2">
              {testScenarios.map((scenario, index) => (
                <Card key={index} className={`cursor-pointer transition-all hover:shadow-md ${scenario.color}`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-2">
                          {scenario.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-3">
                          {scenario.description}
                        </p>
                        <Button
                          onClick={() => handleTestScenario(scenario.path)}
                          size="sm"
                          variant="outline"
                          className="w-full"
                        >
                          测试此场景
                          <ArrowRight className="w-3 h-3 ml-2" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 返回注册页面 */}
            <div className="text-center pt-4 border-t">
              <Button
                onClick={handleBackToRegister}
                variant="ghost"
                className="text-gray-600 hover:text-gray-900"
              >
                返回注册页面
              </Button>
            </div>

            {/* 使用说明 */}
            <div className="bg-blue-50 rounded-lg p-4 text-sm text-blue-800">
              <h4 className="font-semibold mb-2">使用说明：</h4>
              <ul className="space-y-1">
                <li>• 点击上方的测试场景卡片来体验不同的页面状态</li>
                <li>• 修改测试邮箱地址来查看不同邮箱的显示效果</li>
                <li>• 每个页面都包含完整的用户交互和错误处理逻辑</li>
                <li>• 在实际环境中，这些页面会根据真实的API响应进行跳转</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 页面元数据
export const metadata = {
  title: '邮箱验证流程测试 - SpecificAI',
  description: '测试和演示邮箱验证注册流程的各个页面和状态',
}
