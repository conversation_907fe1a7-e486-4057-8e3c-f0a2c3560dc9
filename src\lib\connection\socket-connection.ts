/**
 * Socket.IO连接管理器实现
 * 连接到Python FastAPI后端的具体实现
 */

import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import type { Socket } from 'socket.io-client'
import { createSocketClient, type SocketConfig } from '../socket'
import { MessageAdapter } from '../websocket/message-adapter'
import { NamingAdapter } from '../websocket/naming-adapter'
import { BaseConnection } from './base-connection'
import {
  ConnectionStatus,
  ConnectionType,
  type ConnectionConfig,
  type ConnectionResult,
} from './types'

export class SocketConnection extends BaseConnection {
  private socket: Socket | null = null
  private connectionTimeout: NodeJS.Timeout | null = null

  constructor(private socketConfig?: SocketConfig) {
    super()
  }

  // ============================================================================
  // IConnectionManager 实现
  // ============================================================================

  async connect(config?: ConnectionConfig): Promise<ConnectionResult> {
    if (this.isDestroyed) {
      throw new Error('Connection manager has been destroyed')
    }

    if (this.socket?.connected) {
      return {
        success: true,
        type: ConnectionType.SOCKET,
        message: 'Already connected',
      }
    }

    try {
      this.setStatus(ConnectionStatus.CONNECTING)
      this.config = { ...this.config, ...config }

      // 创建Socket.IO客户端
      const socketConfig = this.getSocketConfig()
      this.socket = createSocketClient(socketConfig)

      // 设置事件监听器
      this.setupSocketEvents()

      // 连接到服务器
      await this.performConnection()

      this.recordConnection()
      this.setStatus(ConnectionStatus.CONNECTED)

      // 设置消息适配器上下文
      if (this.config.auth) {
        MessageAdapter.setContext({
          userId: this.config.auth.userId || '',
          groupChatId: this.config.auth.groupChatId || '',
          organizationId: this.config.auth.organizationId || '',
        })
      }

      return {
        success: true,
        type: ConnectionType.SOCKET,
        message: `Connected to ${socketConfig.url}`,
      }
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR)
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error'

      return {
        success: false,
        type: ConnectionType.SOCKET,
        error: errorMessage,
      }
    }
  }

  async disconnect(): Promise<void> {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    if (this.socket) {
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
    }

    // 清理消息适配器上下文
    MessageAdapter.clearContext()

    this.setStatus(ConnectionStatus.DISCONNECTED)
  }

  async sendMessage(message: BaseWebSocketMessage): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    if (!this.validateMessage(message)) {
      throw new Error('Invalid message format')
    }

    return new Promise((resolve, reject) => {
      // 使用消息适配器转换消息格式（前端 -> 后端）
      const adaptedMessage = MessageAdapter.adaptOutgoingMessage(message)
      
      console.log('📤 Sending adapted message:', adaptedMessage)
      
      // 发送到后端
      this.socket!.emit('user_message', adaptedMessage, (response: any) => {
        if (response?.error) {
          reject(new Error(response.error))
        } else {
          this.incrementMessagesSent()
          resolve()
        }
      })
    })
  }

  /**
   * 发送用户交互响应
   * @param interactionData 交互数据，包含step和selection
   */
  async sendUserInteraction(interactionData: { step: string; selection: string; [key: string]: any }): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    return new Promise((resolve, reject) => {
      // 使用消息适配器转换交互数据格式
      const adaptedInteraction = MessageAdapter.createUserInteractionMessage(interactionData)
      
      console.log('🤝 Sending user interaction:', adaptedInteraction)
      
      this.socket!.emit('user_interaction', adaptedInteraction, (response: any) => {
        if (response?.error) {
          reject(new Error(response.error))
        } else {
          resolve()
        }
      })
    })
  }

  getType(): ConnectionType {
    return ConnectionType.SOCKET
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  private getSocketConfig(): SocketConfig {
    if (this.socketConfig) {
      return this.socketConfig
    }

    const url = this.config.url || process.env.NEXT_PUBLIC_SOCKET_URL
    if (!url) {
      throw new Error('Socket URL not configured')
    }

    const socketConfig: SocketConfig = {
      url,
      timeout: this.config.timeout || 20000,
      reconnection: this.config.reconnection ?? true,
      reconnectionAttempts: this.config.reconnectionAttempts || 5,
      reconnectionDelay: this.config.reconnectionDelay || 1000,
    }

    // 使用命名适配器处理认证参数转换
    if (this.config.auth) {
      // 使用命名适配器转换认证参数（camelCase -> snake_case）
      const backendAuth = NamingAdapter.convertAuthToBackend(this.config.auth)

      // 验证必需的认证参数（后端要求的snake_case格式）
      const requiredFields = ['user_id', 'group_chat_id', 'organization_id'] as const
      const missingFields = requiredFields.filter(field => !(backendAuth as any)[field])

      if (missingFields.length > 0) {
        throw new Error(`Missing required auth fields: ${missingFields.join(', ')}. Please provide: userId, groupChatId, organizationId`)
      }

      socketConfig.auth = backendAuth
      console.log('🔐 Socket auth configured:', {
        user_id: backendAuth.user_id,
        group_chat_id: backendAuth.group_chat_id,
        organization_id: backendAuth.organization_id
      })
    } else {
      throw new Error('Authentication parameters are required for socket connection. Please provide: userId, groupChatId, organizationId')
    }

    return socketConfig
  }

  private setupSocketEvents(): void {
    if (!this.socket) return

    // 连接事件
    this.socket.on('connect', () => {
      console.log('✅ Socket.IO connected successfully')
      this.setStatus(ConnectionStatus.CONNECTED)
    })

    this.socket.on('connect_error', error => {
      console.error('❌ Socket.IO connection error:', error)
      this.setStatus(ConnectionStatus.ERROR)
      this.emitError(new Error(`Connection failed: ${error.message}`))
    })

    this.socket.on('disconnect', reason => {
      console.warn('🔌 Socket.IO disconnected:', reason)
      this.setStatus(ConnectionStatus.DISCONNECTED)

      if (reason === 'io server disconnect') {
        // 服务器主动断开，尝试重连
        this.attemptReconnection()
      }
    })

    // 重连事件
    this.socket.on('reconnect_attempt', () => {
      console.log('🔄 Attempting to reconnect...')
      this.setStatus(ConnectionStatus.RECONNECTING)
    })

    this.socket.on('reconnect', attemptNumber => {
      console.log('✅ Reconnected after', attemptNumber, 'attempts')
      this.recordReconnect()
      this.setStatus(ConnectionStatus.CONNECTED)
    })

    this.socket.on('reconnect_failed', () => {
      console.error('❌ Reconnection failed')
      this.setStatus(ConnectionStatus.ERROR)
      this.emitError(new Error('Reconnection failed after maximum attempts'))
    })

    // 业务消息事件 - 根据Python后端的事件类型设置
    this.setupBusinessEvents()
  }

  private setupBusinessEvents(): void {
    if (!this.socket) return

    // 获取支持的后端事件列表
    const supportedEvents = MessageAdapter.getSupportedBackendEvents()
    
    // 为每个支持的事件设置监听器
    supportedEvents.forEach(event => {
      this.socket!.on(event, (data: any) => {
        try {
          console.log(`📨 Received ${event}:`, data)
          
          // 使用消息适配器转换后端事件为前端格式
          const adaptedMessage = MessageAdapter.adaptIncomingMessage(event, data)
          
          console.log('📤 Adapted to frontend format:', adaptedMessage)
          
          // 发送给前端
          this.emitMessage(adaptedMessage)
        } catch (error) {
          console.error(`❌ Error adapting ${event}:`, error)
          // 创建错误消息
          const errorMessage = MessageAdapter.adaptIncomingMessage('error', {
            code: 'MESSAGE_ADAPTATION_ERROR',
            message: `Failed to adapt ${event}: ${error instanceof Error ? error.message : 'Unknown error'}`
          })
          this.emitMessage(errorMessage)
        }
      })
    })

    // 处理未知事件（调试用）
    this.socket.onAny((event: string, data: any) => {
      if (!supportedEvents.includes(event)) {
        console.warn(`⚠️ Received unsupported event: ${event}`, data)
      }
    })
  }

  private performConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not initialized'))
        return
      }

      // 设置连接超时
      this.connectionTimeout = setTimeout(() => {
        reject(new Error('Connection timeout'))
      }, this.config.timeout || 20000)

      // 监听连接成功
      const onConnect = () => {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout)
          this.connectionTimeout = null
        }
        this.socket!.off('connect', onConnect)
        this.socket!.off('connect_error', onError)
        resolve()
      }

      // 监听连接失败
      const onError = (error: Error) => {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout)
          this.connectionTimeout = null
        }
        this.socket!.off('connect', onConnect)
        this.socket!.off('connect_error', onError)
        reject(error)
      }

      this.socket.on('connect', onConnect)
      this.socket.on('connect_error', onError)

      // 开始连接
      this.socket.connect()
    })
  }

  private attemptReconnection(): void {
    if (this.socket && this.config.reconnection !== false) {
      console.log('🔄 Attempting manual reconnection...')
      this.socket.connect()
    }
  }

  // ============================================================================
  // 资源清理
  // ============================================================================

  destroy(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    super.destroy()
  }
}
