/**
 * 邮箱验证成功页面
 * 显示验证成功状态并提供后续操作选项
 */

import { Suspense } from 'react'
import { VerifySuccessClient } from './verify-success-client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle } from 'lucide-react'

// 服务端组件 - 处理页面结构和SEO
export default function VerifySuccessPage() {
  return (
    <div className="min-h-svh flex flex-col items-center justify-center p-4 sm:p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card className="shadow-lg border-green-200">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              邮箱验证成功！
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              欢迎加入 SpecificAI，您的账户已激活
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Suspense fallback={<VerifySuccessFallback />}>
              <VerifySuccessClient />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 加载状态组件
function VerifySuccessFallback() {
  return (
    <div className="space-y-6">
      <div className="bg-green-50 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-green-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-green-200 rounded w-full mb-1"></div>
          <div className="h-3 bg-green-200 rounded w-5/6"></div>
        </div>
      </div>
      
      <div className="space-y-3">
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  )
}

// 页面元数据
export const metadata = {
  title: '验证成功 - SpecificAI',
  description: '邮箱验证成功，欢迎使用 SpecificAI',
}
