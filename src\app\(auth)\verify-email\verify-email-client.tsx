/**
 * 邮箱验证处理页面 - 客户端组件
 * 处理验证token，调用API并跳转到结果页面
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { AuthService, handleAuthError } from '@/lib/services/auth-service'

type VerificationState = 'verifying' | 'success' | 'error'

export function VerifyEmailClient() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const t = useTranslations('auth.verification')
  
  const [state, setState] = useState<VerificationState>('verifying')
  const [error, setError] = useState<string>('')
  
  // 从URL参数获取验证token
  const token = searchParams.get('token')

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setError('验证链接无效，缺少验证token')
        setState('error')
        // 3秒后跳转到失败页面
        setTimeout(() => {
          router.push('/verify-failed?error=invalid_token')
        }, 3000)
        return
      }

      try {
        setState('verifying')
        
        // 调用验证API
        const response = await AuthService.verifyEmail(token)
        
        if (response.success && response.verified) {
          setState('success')
          // 2秒后跳转到成功页面
          setTimeout(() => {
            router.push('/verify-success')
          }, 2000)
        } else {
          throw new Error(response.message || '验证失败')
        }
      } catch (error) {
        console.error('邮箱验证失败:', error)
        const errorMessage = handleAuthError(error)
        setError(errorMessage)
        setState('error')
        
        // 3秒后跳转到失败页面
        setTimeout(() => {
          const errorParam = encodeURIComponent(errorMessage)
          router.push(`/verify-failed?error=${errorParam}`)
        }, 3000)
      }
    }

    verifyEmail()
  }, [token, router])

  const renderContent = () => {
    switch (state) {
      case 'verifying':
        return (
          <div className="text-center space-y-4">
            <h1 className="text-xl font-semibold text-gray-900">
              正在验证您的邮箱...
            </h1>
            <p className="text-gray-600">
              请稍候，这只需要几秒钟
            </p>
            <div className="flex justify-center">
              <div className="w-8 h-1 bg-blue-200 rounded-full overflow-hidden">
                <div className="w-full h-full bg-blue-600 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        )

      case 'success':
        return (
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                验证成功！
              </h1>
              <p className="text-gray-600">
                您的邮箱已成功验证，正在跳转...
              </p>
            </div>
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
          </div>
        )

      case 'error':
        return (
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                验证失败
              </h1>
              <p className="text-gray-600 mb-2">
                {error || '验证过程中出现错误'}
              </p>
              <p className="text-sm text-gray-500">
                正在跳转到错误页面...
              </p>
            </div>
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600 mx-auto"></div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="py-4">
      {renderContent()}
    </div>
  )
}
