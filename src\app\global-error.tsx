'use client'

import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import { useEffect, useState } from 'react'
import * as Sentry from '@sentry/nextjs'

export default function Error({
  error,
  reset,
}: {
  error: (Error & { digest?: string }) | null
  reset: () => void
}) {
  const t = useTranslations('ErrorPage')
  const [eventId, setEventId] = useState<string | null>(null)

  useEffect(() => {
    if (error) {
      const id = Sentry.captureException(error)
      setEventId(id)
    }
  }, [error])

  return (
    <div className="h-screen overflow-hidden bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900 dark:text-white">
            {t('title')}
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-300">
            {t('subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg">
              <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {t('errorDetails')}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400 font-mono break-all">
                {error.message}
              </p>
              {/* {eventId && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Sentry Event ID: {eventId}
                </p>
              )} */}
            </div>
          )}

          <div className="flex flex-col space-y-2">
            <Button onClick={reset} className="w-full" variant="default">
              <RefreshCw className="mr-2 h-4 w-4" />
              {t('tryAgain')}
            </Button>

            <Button
              onClick={() => {
                if (typeof window !== 'undefined') {
                  window.location.href = '/login'
                }
              }}
              className="w-full"
              variant="outline"
            >
              <Home className="mr-2 h-4 w-4" />
              {t('goHome')}
            </Button>
          </div>

          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            {t('footerMessage')}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
