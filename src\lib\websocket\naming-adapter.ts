/**
 * 命名约定转换适配器
 * 
 * 设计原则：
 * - 前端内部统一使用 camelCase 命名约定
 * - 与后端通信时自动转换为 snake_case 格式
 * - 提供双向转换能力，确保数据格式一致性
 */

// ============================================================================
// 核心字段映射配置
// ============================================================================

// 前端 camelCase -> 后端 snake_case
const CAMEL_TO_SNAKE_MAP: Record<string, string> = {
  userId: 'user_id',
  groupChatId: 'group_chat_id', 
  organizationId: 'organization_id',
  sessionId: 'session_id',
  messageId: 'message_id',
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  isComplete: 'is_complete',
  defaultValue: 'default_value',
  fieldId: 'field_id',
  fieldType: 'field_type',
  fieldLabel: 'field_label',
  fieldRequired: 'field_required',
  errorCode: 'error_code',
  errorMessage: 'error_message',
}

// 后端 snake_case -> 前端 camelCase
const SNAKE_TO_CAMEL_MAP: Record<string, string> = Object.fromEntries(
  Object.entries(CAMEL_TO_SNAKE_MAP).map(([camel, snake]) => [snake, camel])
)

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 通用 camelCase 转 snake_case
 */
function camelToSnake(str: string): string {
  // 先检查预定义映射
  if (CAMEL_TO_SNAKE_MAP[str]) {
    return CAMEL_TO_SNAKE_MAP[str]
  }
  
  // 通用转换规则
  return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
}

/**
 * 通用 snake_case 转 camelCase
 */
function snakeToCamel(str: string): string {
  // 先检查预定义映射
  if (SNAKE_TO_CAMEL_MAP[str]) {
    return SNAKE_TO_CAMEL_MAP[str]
  }
  
  // 通用转换规则
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 判断是否为对象（非数组、非null）
 */
function isObject(value: any): value is Record<string, any> {
  return value !== null && typeof value === 'object' && !Array.isArray(value)
}

// ============================================================================
// 命名转换适配器类
// ============================================================================

export class NamingAdapter {
  
  // ============================================================================
  // 对象级别转换（深度递归）
  // ============================================================================
  
  /**
   * 将对象从 camelCase 转换为 snake_case
   * @param obj 前端对象（camelCase格式）
   * @returns 后端对象（snake_case格式）
   */
  static toSnakeCase<T = any>(obj: Record<string, any>): T {
    if (!isObject(obj)) {
      return obj as T
    }

    const result: Record<string, any> = {}

    for (const [key, value] of Object.entries(obj)) {
      const snakeKey = camelToSnake(key)
      
      if (Array.isArray(value)) {
        // 处理数组，递归转换数组中的对象
        result[snakeKey] = value.map(item => 
          isObject(item) ? this.toSnakeCase(item) : item
        )
      } else if (isObject(value)) {
        // 递归处理嵌套对象
        result[snakeKey] = this.toSnakeCase(value)
      } else {
        // 基本类型直接赋值
        result[snakeKey] = value
      }
    }

    return result as T
  }

  /**
   * 将对象从 snake_case 转换为 camelCase
   * @param obj 后端对象（snake_case格式）
   * @returns 前端对象（camelCase格式）
   */
  static toCamelCase<T = any>(obj: Record<string, any>): T {
    if (!isObject(obj)) {
      return obj as T
    }

    const result: Record<string, any> = {}

    for (const [key, value] of Object.entries(obj)) {
      const camelKey = snakeToCamel(key)
      
      if (Array.isArray(value)) {
        // 处理数组，递归转换数组中的对象
        result[camelKey] = value.map(item => 
          isObject(item) ? this.toCamelCase(item) : item
        )
      } else if (isObject(value)) {
        // 递归处理嵌套对象
        result[camelKey] = this.toCamelCase(value)
      } else {
        // 基本类型直接赋值
        result[camelKey] = value
      }
    }

    return result as T
  }

  // ============================================================================
  // 认证参数专用转换
  // ============================================================================

  /**
   * 转换认证参数：前端 camelCase -> 后端 snake_case
   */
  static convertAuthToBackend(auth: {
    userId?: string
    groupChatId?: string
    organizationId?: string
    token?: string
    [key: string]: any
  }) {
    return {
      user_id: auth.userId,
      group_chat_id: auth.groupChatId,
      organization_id: auth.organizationId,
      token: auth.token,
      // 保留其他未映射的字段
      ...Object.fromEntries(
        Object.entries(auth).filter(([key]) => 
          !['userId', 'groupChatId', 'organizationId', 'token'].includes(key)
        )
      )
    }
  }

  /**
   * 转换认证参数：后端 snake_case -> 前端 camelCase
   */
  static convertAuthToFrontend(auth: {
    user_id?: string
    group_chat_id?: string
    organization_id?: string
    token?: string
    [key: string]: any
  }) {
    return {
      userId: auth.user_id,
      groupChatId: auth.group_chat_id,
      organizationId: auth.organization_id,
      token: auth.token,
      // 保留其他未映射的字段
      ...Object.fromEntries(
        Object.entries(auth).filter(([key]) => 
          !['user_id', 'group_chat_id', 'organization_id', 'token'].includes(key)
        )
      )
    }
  }

  // ============================================================================
  // 消息字段专用转换
  // ============================================================================

  /**
   * 转换WebSocket消息的核心字段
   */
  static convertMessageFields(message: Record<string, any>, direction: 'toBackend' | 'toFrontend') {
    if (direction === 'toBackend') {
      return {
        ...message,
        user_id: message.userId,
        group_chat_id: message.groupChatId,
        organization_id: message.organizationId,
        session_id: message.sessionId,
        message_id: message.messageId || message.id,
        created_at: message.createdAt,
        updated_at: message.updatedAt,
      }
    } else {
      return {
        ...message,
        userId: message.user_id,
        groupChatId: message.group_chat_id,
        organizationId: message.organization_id,
        sessionId: message.session_id,
        messageId: message.message_id || message.id,
        createdAt: message.created_at,
        updatedAt: message.updated_at,
      }
    }
  }

  // ============================================================================
  // 类型安全的批量转换
  // ============================================================================

  /**
   * 批量转换对象数组
   */
  static convertArray<T>(
    items: Record<string, any>[], 
    direction: 'toSnakeCase' | 'toCamelCase'
  ): T[] {
    return items.map(item => 
      direction === 'toSnakeCase' 
        ? this.toSnakeCase<T>(item) 
        : this.toCamelCase<T>(item)
    )
  }

  // ============================================================================
  // 调试和验证工具
  // ============================================================================

  /**
   * 验证对象的命名约定
   */
  static validateNamingConvention(
    obj: Record<string, any>, 
    expectedConvention: 'camelCase' | 'snake_case'
  ): { valid: boolean; violations: string[] } {
    const violations: string[] = []
    
    const checkKey = (key: string) => {
      if (expectedConvention === 'camelCase') {
        // camelCase: 首字母小写，不包含下划线
        if (key.includes('_') || /^[A-Z]/.test(key)) {
          violations.push(`Key "${key}" violates camelCase convention`)
        }
      } else {
        // snake_case: 全小写，可包含下划线
        if (/[A-Z]/.test(key)) {
          violations.push(`Key "${key}" violates snake_case convention`)
        }
      }
    }

    const traverse = (obj: any, path = '') => {
      if (isObject(obj)) {
        Object.keys(obj).forEach(key => {
          checkKey(key)
          traverse(obj[key], path ? `${path}.${key}` : key)
        })
      } else if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
          traverse(item, `${path}[${index}]`)
        })
      }
    }

    traverse(obj)

    return {
      valid: violations.length === 0,
      violations
    }
  }

  /**
   * 生成转换示例（用于调试）
   */
  static generateConversionExample() {
    const frontendExample = {
      userId: 'user123',
      groupChatId: 'group456', 
      organizationId: 'org789',
      sessionId: 'session001',
      messageData: {
        isComplete: true,
        defaultValue: 'hello',
        createdAt: '2024-01-01T00:00:00Z'
      }
    }

    const backendExample = this.toSnakeCase(frontendExample)
    const roundTrip = this.toCamelCase(backendExample)

    return {
      original: frontendExample,
      converted: backendExample,
      roundTrip,
      isConsistent: JSON.stringify(frontendExample) === JSON.stringify(roundTrip)
    }
  }
}

// ============================================================================
// 便捷导出函数
// ============================================================================

/**
 * 快捷转换：camelCase -> snake_case
 */
export const toSnakeCase = <T = any>(obj: Record<string, any>): T => 
  NamingAdapter.toSnakeCase<T>(obj)

/**
 * 快捷转换：snake_case -> camelCase
 */
export const toCamelCase = <T = any>(obj: Record<string, any>): T => 
  NamingAdapter.toCamelCase<T>(obj)

/**
 * 快捷认证转换：前端 -> 后端
 */
export const authToBackend = (auth: Record<string, any>) => 
  NamingAdapter.convertAuthToBackend(auth)

/**
 * 快捷认证转换：后端 -> 前端
 */
export const authToFrontend = (auth: Record<string, any>) => 
  NamingAdapter.convertAuthToFrontend(auth)