/**
 * 虚拟化消息列表组件 - 支持大量消息的高性能渲染
 * 基于react-window实现
 */

'use client'

import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'
import { forwardRef, memo, useImperativeHandle, useRef } from 'react'
import AutoSizer from 'react-virtualized-auto-sizer'
import { FixedSizeList as List, ListChildComponentProps } from 'react-window'

// 使用统一的消息组件
import Message from './message'

// UI组件
import { Loader2 } from 'lucide-react'

// 类型
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface VirtualizedMessageListProps {
  /** 消息数据 */
  messages: BaseWebSocketMessage[]
  
  /** 显示配置 */
  showAvatar?: boolean
  showTimestamp?: boolean
  enableCopy?: boolean
  
  /** 加载状态 */
  isLoading?: boolean
  
  /** 自定义样式 */
  className?: string
  
  /** 消息复制回调 */
  onCopy?: (content: string) => void

  /** 消息项高度（像素）*/
  itemHeight?: number
}

export interface VirtualizedMessageListRef {
  scrollToBottom: () => void
  scrollToIndex: (index: number) => void
}

// ============================================================================
// 消息项组件
// ============================================================================

interface MessageItemProps extends ListChildComponentProps {
  data: {
    messages: BaseWebSocketMessage[]
    showAvatar: boolean
    showTimestamp: boolean
    enableCopy: boolean
    onCopy?: (content: string) => void
  }
}

const MessageItem = memo<MessageItemProps>(({ index, style, data }) => {
  const { messages, showAvatar, showTimestamp, enableCopy, onCopy } = data
  const message = messages[index]

  if (!message) {
    return <div style={style} />
  }

  return (
    <div style={style}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="px-4 py-2"
      >
        <Message
          message={message}
          showAvatar={showAvatar}
          showTimestamp={showTimestamp}
          enableCopy={enableCopy}
          onCopy={onCopy}
        />
      </motion.div>
    </div>
  )
})

MessageItem.displayName = 'MessageItem'

// ============================================================================
// 虚拟化消息列表组件
// ============================================================================

export const VirtualizedMessageList = memo(forwardRef<VirtualizedMessageListRef, VirtualizedMessageListProps>(({
  messages,
  showAvatar = true,
  showTimestamp = true,
  enableCopy = true,
  isLoading = false,
  className,
  onCopy = undefined,
  itemHeight = 120, // 默认消息项高度
}, ref) => {
  const listRef = useRef<List>(null)

  // 暴露滚动方法
  useImperativeHandle(ref, () => ({
    scrollToBottom: () => {
      if (listRef.current && messages.length > 0) {
        listRef.current.scrollToItem(messages.length - 1, 'end')
      }
    },
    scrollToIndex: (index: number) => {
      if (listRef.current) {
        listRef.current.scrollToItem(index, 'start')
      }
    },
  }), [messages.length])
  
  // 消息项数据
  const itemData: {
    messages: BaseWebSocketMessage[]
    showAvatar: boolean
    showTimestamp: boolean
    enableCopy: boolean
    onCopy?: (content: string) => void
  } = {
    messages,
    showAvatar,
    showTimestamp,
    enableCopy,
    ...(onCopy && { onCopy }),
  }

  // 如果没有消息且不在加载中，显示欢迎消息
  if (!messages.length && !isLoading) {
    return (
      <div className={cn(
        'flex flex-col items-center justify-center h-full text-center',
        className
      )}>
        <div className="max-w-md space-y-4">
          <div className="text-4xl">👋</div>
          <div className="text-lg font-medium text-foreground">
            欢迎使用聊天助手
          </div>
          <div className="text-sm text-muted-foreground">
            开始对话，我将为您提供帮助和分析
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('h-full', className)}>
      {/* 虚拟化消息列表 */}
      <AutoSizer>
        {({ height, width }) => (
          <List
            ref={listRef}
            height={height}
            width={width}
            itemCount={messages.length}
            itemSize={itemHeight}
            itemData={itemData}
            overscanCount={5} // 预渲染前后5个项目以提高滚动性能
          >
            {MessageItem}
          </List>
        )}
      </AutoSizer>

      {/* 加载指示器覆盖层 */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center justify-center gap-2 bg-background/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg"
        >
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">
            正在处理您的请求...
          </span>
        </motion.div>
      )}
    </div>
  )
}))

VirtualizedMessageList.displayName = 'VirtualizedMessageList'

export default VirtualizedMessageList