/**
 * 优化的步骤指示器组件
 * 性能优化：memo化、减少重渲染
 */

import { memo } from 'react'
import { cn } from '@/lib/utils'

interface StepIndicatorProps {
  currentStep: number
  totalSteps: number
  className?: string
}

const StepIndicator = memo(function StepIndicator({ 
  currentStep, 
  totalSteps, 
  className 
}: StepIndicatorProps) {
  // 避免在渲染过程中创建数组
  const steps = Array.from({ length: totalSteps }, (_, index) => index)

  return (
    <div className={cn("flex items-center justify-center", className)}>
      <div className="flex items-center space-x-2">
        {steps.map((index) => (
          <div key={index} className="flex items-center">
            <div
              className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                index <= currentStep
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-500'
              )}
            >
              {index + 1}
            </div>
            {index < totalSteps - 1 && (
              <div
                className={cn(
                  'w-8 h-0.5 mx-2',
                  index < currentStep ? 'bg-blue-500' : 'bg-gray-200'
                )}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
})

StepIndicator.displayName = 'StepIndicator'

export default StepIndicator