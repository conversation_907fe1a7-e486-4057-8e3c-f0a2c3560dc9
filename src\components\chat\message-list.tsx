/**
 * 消息列表组件 - 使用统一的message.tsx组件
 * 基于新的chat-store.ts架构重构
 */

'use client'

import React, { memo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// 使用统一的消息组件
import Message from './message'

// UI组件
import { Loader2 } from 'lucide-react'

// 类型
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface MessageListProps {
  /** 消息数据 */
  messages: BaseWebSocketMessage[]
  
  /** 显示配置 */
  showAvatar?: boolean
  showTimestamp?: boolean
  enableCopy?: boolean
  
  /** 加载状态 */
  isLoading?: boolean
  
  /** 自定义样式 */
  className?: string
  
  /** 消息复制回调 */
  onCopy?: (content: string) => void
}

// ============================================================================
// 消息列表组件
// ============================================================================

export const MessageList = memo<MessageListProps>(({
  messages,
  showAvatar = true,
  showTimestamp = true,
  enableCopy = true,
  isLoading = false,
  className,
  onCopy = undefined,
}) => {
  // 如果没有消息且不在加载中，显示欢迎消息
  if (!messages.length && !isLoading) {
    return (
      <div className={cn(
        'flex flex-col items-center justify-center h-full text-center',
        className
      )}>
        <div className="max-w-md space-y-4">
          <div className="text-4xl">👋</div>
          <div className="text-lg font-medium text-foreground">
            欢迎使用聊天助手
          </div>
          <div className="text-sm text-muted-foreground">
            开始对话，我将为您提供帮助和分析
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 消息列表 */}
      <AnimatePresence mode="popLayout">
        {messages.map((message) => (
          <motion.div
            key={message.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Message
              message={message}
              showAvatar={showAvatar}
              showTimestamp={showTimestamp}
              enableCopy={enableCopy}
              onCopy={onCopy}
            />
          </motion.div>
        ))}
      </AnimatePresence>

      {/* 加载指示器 */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-center gap-2 p-4"
        >
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">
            正在处理您的请求...
          </span>
        </motion.div>
      )}
    </div>
  )
})

MessageList.displayName = 'MessageList'

export default MessageList