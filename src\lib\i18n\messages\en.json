{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "close": "Close", "search": "Search", "filter": "Filter", "refresh": "Refresh", "retry": "Retry"}, "auth": {"login": {"title": "Welcome Back", "subtitle": "Sign in to your Vibe Coding account", "username": "Username or Email", "usernamePlaceholder": "Enter username or email", "password": "Password", "passwordPlaceholder": "Enter password", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "orLoginWith": "Or login with", "loginWithApple": "Login with Apple", "loginWithGoogle": "Login with Google", "loginWithFacebook": "Login with Facebook", "noAccount": "Don't have an account?", "registerNow": "Register now", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "Enterprise AI Application Frontend Architecture", "feature1": "✨ Complete Vue3 to Next.js Migration", "feature2": "🚀 Modern Component System", "feature3": "🔐 Complete Authentication & Permission Management", "feature4": "🎨 Enterprise UI Design System", "termsText": "By continuing, you agree to our", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "period": "."}, "register": {"title": "Create Account", "subtitle": "Sign up for your new account", "name": "Name", "namePlaceholder": "Enter your name", "email": "Email", "emailPlaceholder": "Enter your email address", "password": "Password", "passwordPlaceholder": "Enter your password (at least 6 characters)", "companyName": "Company Name", "companyNamePlaceholder": "Enter your company name", "inviteCode": "Invite Code", "inviteCodePlaceholder": "Enter invite code", "createAccountButton": "Create Account", "creatingAccount": "Creating Account...", "accountCreated": "Account Created!", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign in now", "or": "Or", "registerSuccess": "Registration successful! Redirecting to homepage...", "registering": "Registering..."}, "validation": {"usernameRequired": "Please enter username or email", "passwordRequired": "Please enter password", "emailFormat": "Please enter a valid email address", "passwordLength": "Password must be at least 6 characters", "nameLength": "Name must be at least 2 characters", "companyNameLength": "Company name must be at least 2 characters", "inviteCodeLength": "Invite code format is incorrect", "loginError": "An error occurred during login, please try again later", "allFieldsRequired": "Please fill in all required fields", "passwordMismatch": "Passwords do not match", "registerError": "Registration failed, please check network connection or contact administrator"}, "messages": {"loginSuccess": "Login successful", "welcomeBack": "Welcome back, {username}!", "loginFailed": "<PERSON><PERSON> failed", "loginError": "An error occurred during login", "featureInDevelopment": "Feature in Development", "forgotPasswordInDevelopment": "Forgot password feature is under development", "registerInDevelopment": "Registration feature is under development"}, "emailVerification": {"pending": {"title": "Verify Your Email Address", "subtitle": "We've sent a verification email to your inbox", "emailSentTo": "Verification email sent to", "nextSteps": "What's next?", "step1": "Check your email (including spam folder)", "step2": "Click the 'Verify Email' button in the email", "step3": "Complete verification to start using your account", "resendEmail": "<PERSON><PERSON><PERSON>", "resendEmailCooldown": "Resend Email ({seconds}s)", "resending": "Sending...", "backToLogin": "Back to Login", "checkingStatus": "Checking verification status...", "noEmailReceived": "Didn't receive the email? Please check your spam folder", "needHelp": "Need help? Please", "contactSupport": "contact support"}, "failed": {"title": "Email Verification Failed", "subtitle": "There was a problem sending the verification email", "emailFailedTo": "Failed to send verification email to", "errorDetails": "Error Details:", "possibleReasons": "Possible Reasons", "reason1": "Email service temporarily unavailable", "reason2": "Email address may have issues", "reason3": "Unstable network connection", "reason4": "Email server configuration issues", "solutions": "Solutions", "solution1": "Resend verification email to get a new verification link", "solution2": "Check if there are other verification emails in your inbox", "solution3": "Try logging in directly (if account is already verified)", "solution4": "Register again with a different email address", "retryEmail": "Resend Verification Email", "backToRegister": "Back to Register", "backToLogin": "Back to Login", "troubleshooting": "If the problem persists, please try:", "troubleshoot1": "Check if the email address is correct", "troubleshoot2": "Register again with a different email address", "troubleshoot3": "Try again later"}, "processing": {"title": "Verifying Your Email...", "subtitle": "Please wait, this will only take a few seconds", "verifying": "Verifying your email...", "success": "Verification Successful!", "successMessage": "Your email has been successfully verified, redirecting...", "failed": "Verification Failed", "redirecting": "Redirecting to error page..."}, "success": {"title": "Email Verification Successful!", "subtitle": "Welcome to SpecificAI, your account is now activated", "welcomeMessage": "You can now log in to your account and start experiencing the powerful features of SpecificAI.", "features": "You can experience:", "feature1": "Intelligent AI conversations and content generation", "feature2": "Team collaboration and project management", "feature3": "Personalized workflow customization", "autoRedirect": "Auto-redirecting to login page in {seconds} seconds", "cancelAutoRedirect": "Cancel", "loginNow": "Login Now", "goToDashboard": "Go to Dashboard", "helpDocs": "Help Documentation", "contactSupport": "Contact Support"}, "verifyFailed": {"title": "Invalid Verification Link", "subtitle": "The verification link may have expired or been used", "invalidToken": "Invalid verification link format", "invalidTokenDesc": "The verification link may have been truncated or corrupted", "tokenExpired": "Verification link has expired", "tokenExpiredDesc": "Verification links are valid for 1 hour", "tokenUsed": "Verification link has been used", "tokenUsedDesc": "This verification link has already been used", "defaultError": "Verification failed", "defaultErrorDesc": "An unknown error occurred during verification", "possibleReasons": "Possible reasons:", "reason1": "Verification link has expired (valid for 1 hour)", "reason2": "Verification link has been used", "reason3": "Incorrect link format", "reason4": "Network connection issues", "solutions": "Solutions", "solution1": "Resend verification email to get a new verification link", "solution2": "Check if there are other verification emails in your inbox", "solution3": "Try logging in directly (if account is already verified)", "solution4": "Register again with a different email address", "resendEmail": "Resend Verification Email", "enterEmail": "Email Address", "enterEmailPlaceholder": "Please enter your email address", "tryLogin": "<PERSON>", "backToRegister": "Register Again"}, "test": {"title": "Email Verification Flow Test", "subtitle": "Test and demonstrate the complete email verification registration flow", "testEmail": "Test Email Address", "testEmailPlaceholder": "Enter test email address", "testEmailDesc": "This email address will be used to test the display effects of various pages", "pendingScenario": "Email Verification Pending Page", "pendingScenarioDesc": "Simulate the waiting state after successful email sending", "failedScenario": "Email Sending Failed Page", "failedScenarioDesc": "Simulate email sending failure scenarios", "processingScenario": "Email Verification Processing Page", "processingScenarioDesc": "Simulate processing verification links from emails", "successScenario": "Verification Success Page", "successScenarioDesc": "Simulate successful email verification state", "verifyFailedScenario": "Verification Failed Page", "verifyFailedScenarioDesc": "Simulate email verification failure scenarios", "testScenario": "Test This <PERSON><PERSON>rio", "backToRegister": "Back to Register", "instructions": "Instructions:", "instruction1": "Click the test scenario cards above to experience different page states", "instruction2": "Modify the test email address to see different email display effects", "instruction3": "Each page contains complete user interaction and error handling logic", "instruction4": "In production, these pages will redirect based on real API responses"}}, "legacy": {"login": "<PERSON><PERSON>", "logout": "Logout", "pleaseLogin": "Please login first", "invalidCredentials": "Invalid username or password", "accountLocked": "Account has been locked, please contact administrator", "userDeleted": "This user has been deleted, please contact administrator"}}, "navigation": {"chat": "Cha<PERSON>", "uiGallery": "UI Gallery", "home": "Home", "dashboard": "Dashboard", "settings": "Settings"}, "chat": {"title": "Chat Interface", "subtitle": "Coming soon...", "welcome": "Welcome to SpecificAI!", "description": "Your AI-powered assistant is ready to help.", "placeholder": "Type a message...", "send": "Send", "clear": "Clear chat", "typing": "Typing...", "security": {"error": {"noUser": "User information not found, please login again", "noOrganization": "Organization information not found, please contact administrator", "invalidGroupId": "Invalid chat group ID, please refresh and try again", "unknown": "Security validation failed, unknown error"}}, "status": {"loading": "Loading...", "validating": "Validating permissions...", "connecting": "Connecting..."}, "error": {"title": "An Error Occurred", "reload": "Reload", "initializationFailed": "Chat initialization failed: {error}", "unknown": "Unknown error", "reconnect": "Reconnect", "boundary": {"connection": {"title": "Network Connection Error", "message": "Chat service is temporarily unavailable. Please check your network connection and try again."}, "security": {"title": "Access Permission Error", "message": "You don't have permission to access this chat. Please log in again."}, "render": {"title": "Interface Display Error", "message": "Chat interface encountered a display issue. Attempting to recover."}, "unknown": {"title": "System Error", "message": "Chat system encountered an unknown error. Please try again later."}, "technicalDetails": "Technical Details", "retry": {"connection": "Reconnect", "general": "Retry"}, "reset": "Reset", "refresh": "Refresh Page", "maxRetriesReached": "Maximum retry attempts reached. Please refresh the page or contact support."}}, "interface": {"placeholder": {"group": "Type a message in group {groupId}...", "default": "Type a message to test the intelligent connection system..."}}}, "ui": {"gallery": {"title": "UI Component Gallery", "description": "Test and debug all UI components for style verification", "buttons": "Button Components", "inputs": "Input Components", "cards": "Card Components", "markdown": "Markdown Renderer Demo"}}, "uiGallery": {"title": "UI Component Gallery", "subtitle": "Showcase of available components in the project", "radixComponents": "Radix UI Components", "customComponents": "Custom Components", "componentAvailable": "Component Available"}, "errors": {"unauthorized": "Unauthorized, please login again", "noPermission": "Insufficient permissions to access", "userNotLogin": "User not logged in, please login first", "userDeleted": "This user has been deleted, please contact administrator", "networkError": "Network connection failed, please check network settings", "serverError": "Internal server error", "notFound": "Requested resource not found", "badRequest": "Bad request parameters", "forbidden": "Insufficient permissions", "timeout": "Request timeout", "unknown": "Unknown error"}, "language": {"switch": "Switch Language", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "Current Language"}, "user": {"profile": "Profile", "account": "Account", "preferences": "Preferences", "avatar": "Avatar", "name": "Name", "email": "Email", "role": "Role", "lastLogin": "Last Login", "memberSince": "Member Since"}, "poll": {"title": "Tell Us About Your Company", "subtitle": "Please provide your company information so we can better serve you", "placeholder": "Please describe your company situation or select tags above...", "start": "Start", "step": "Step {step} of {total}", "loading": "Loading survey questions...", "steps": {"country": {"title": "Please select your country/region", "options": {"vietnam": "Vietnam", "japan": "Japan", "korea": "Korea", "eastAsia": "East Asian Countries", "southeastAsia": "Southeast Asia Region"}}, "industry": {"title": "Please select your industry type", "options": {"technology": "Technology/Internet", "manufacturing": "Manufacturing", "finance": "Financial Services", "retail": "Retail/E-commerce", "other": "Other Industries"}}, "focus": {"title": "Please select your areas of focus", "options": {"aiAutomation": "AI Automation", "digitalTransformation": "Digital Transformation", "dataAnalytics": "Data Analytics", "processOptimization": "Process Optimization", "other": "Other Areas"}}, "companyInfo": {"title": "Please describe your company", "placeholder": "Please provide details about your company size, business model, main challenges, etc..."}}, "progress": "Progress", "stepOf": "Step {current} of {total}", "buttons": {"next": "Next", "previous": "Previous", "submit": "Submit", "restart": "<PERSON><PERSON>"}}, "embed": {"initializing": "Initializing...", "loading": "Loading page", "pageNotFound": "The requested page '{page}' could not be found.", "paramError": "Failed to load page due to a parameter error.", "errorTitle": "An Error Occurred"}, "biddingChat": {"showProcess": "Show Analysis Process", "hideProcess": "Hide Analysis Process", "generatingReport": "Generating Final Report...", "scrollToBottom": "Scroll to bottom", "finalReportTitle": "Final Analysis Report", "viewFullReport": "View Full Report", "summaryTitle": "1. Analysis Summary", "backgroundAnalysisTitle": "2. Background Analysis"}, "reportPage": {"defaultTitle": "Report Page", "reportTitle": "Embedded Report", "reportDescription": "This is a simplified report page demonstrating iframe embed functionality.", "parametersTitle": "URL Parameters"}, "resultsPage": {"pageTitle": "Results Page", "resultsTitle": "Analysis Results", "description": "Here are the analysis results and related data.", "parametersTitle": "URL Parameters", "sampleReport1": "Sample Report 1", "sampleReport2": "Sample Report 2"}, "table": {"title": "Title", "status": {"completed": "Completed", "pending": "Pending", "running": "Running", "failed": "Failed"}, "date": "Date", "noData": "No data available"}, "dashboardPage": {"welcomeTitle": "Welcome", "welcomeMessage": "Welcome, {name}!", "dashboardTitle": "Embedded Dashboard", "dashboardDescription": "This is a simplified dashboard page demonstrating iframe embed functionality.", "parametersTitle": "URL Parameters"}, "ErrorPage": {"title": "Something went wrong", "subtitle": "The application encountered an unexpected error. We are working to resolve it.", "errorDetails": "<PERSON><PERSON><PERSON>", "errorDetailsDescription": "The following information may help the support team diagnose the issue.", "errorMessage": "Error Message", "unknownError": "Unknown Error", "errorId": "Error ID", "occurrenceTime": "Time of Occurrence", "retry": "Retry", "backToHome": "Back to Home", "refreshPage": "Refresh Page", "troubleshootingTitle": "Troubleshooting Suggestions", "troubleshooting1": "Refresh the page or try again later.", "troubleshooting2": "Check if your network connection is stable.", "troubleshooting3": "Clear your browser's cache and cookies.", "troubleshooting4": "Try accessing the site with a different browser.", "stillHavingIssuesTitle": "Still having issues?", "stillHavingIssuesDescription": "If the error persists, please contact our technical support team. Provide the error details above for a quick diagnosis.", "sendReport": "Send Error Report", "sending": "Sending...", "reportSent": "Report Sent", "resend": "Resend", "sendingReport": "Sending error report...", "sendReportFailed": "An exception occurred while sending the error report. Please try again later.", "sendFailed": "Failed to send. Please try again later.", "footerCode": "Error Code: 500 | Internal Server Error", "footerMessage": "We have automatically logged this error, and our technical team will address it as soon as possible."}, "AnalysisReportsTable": {"taskName": "Task Name", "newTitle": "New Title", "status": "Status", "progress": "Progress", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions", "completed": "Completed", "running": "Running", "failed": "Failed", "pending": "Pending", "cancelled": "Cancelled"}}