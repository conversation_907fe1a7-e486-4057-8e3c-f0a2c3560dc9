'use client'

import { useEffect, useState, useCallback, useMemo, Suspense } from 'react'
import { notFound } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useChat, useSocket, useChatMessages, useChatSession } from '@/hooks/use-chat'
import { useSession } from '@/components/providers/session-provider'
import Loading from '@/app/loading'

// 动态导入重型组件 - 延连加载优化性能
import dynamic from 'next/dynamic'
const ChatContainer = dynamic(() => import('@/components/chat/chat-container'), {
  loading: () => (
    <div className="flex flex-col h-full animate-pulse">
      <div className="h-16 bg-gray-100 mb-4" />
      <div className="flex-1 space-y-4 p-4">
        <div className="h-20 bg-gray-100 rounded" />
        <div className="h-16 bg-gray-100 rounded" />
      </div>
      <div className="h-20 bg-gray-100 mt-4" />
    </div>
  ),
  ssr: false
})

const ChatErrorBoundary = dynamic(() => import('@/components/error/chat-error-boundary').then(mod => ({ default: mod.ChatErrorBoundary })), {
  loading: () => <div className="h-full" />,
  ssr: false
})

interface ChatPageProps {
  params: Promise<{ groupId: string }>
}

// 🔧 页面状态类型 - 简化为只处理参数解析
type PageState = { status: 'loading' } | { status: 'ready'; groupId: string }

/**
 * 动态群聊页面 - 基于新架构的智能聊天系统
 */
export default function ChatPage({ params }: ChatPageProps) {
  const [pageState, setPageState] = useState<PageState>({ status: 'loading' })

  // 🌐 国际化
  const t = useTranslations('chat')

  // 🎯 获取Better Auth session信息
  const { user, organization, isAuthenticated } = useSession()

  // 🎯 使用统一的聊天Hook
  const { connectToGroup, sendMessage } = useChat()
  const { isConnected, connectionStatus, error: connectionError, connect } = useSocket()
  const { messages } = useChatMessages()
  const { currentSession, hasActiveSession } = useChatSession()

  // 计算衍生状态
  const hasMessages = messages.length > 0

  // 🔧 参数解析 - 简化的初始化逻辑
  useEffect(() => {
    let isCancelled = false

    const parseParams = async () => {
      try {
        const { groupId } = await params

        if (isCancelled) return

        setPageState({ status: 'ready', groupId })
      } catch (error) {
        console.error('❌ 参数解析失败:', error)
        // 参数解析失败，触发 404
        notFound()
      }
    }

    parseParams()

    return () => {
      isCancelled = true
    }
  }, [params])

  // 注意：自动连接和会话管理现在由ChatContainer内部处理
  // 通过features.autoConnect = true 启用自动连接

  // 🔧 缓存的重连处理
  const handleReconnect = useCallback(() => {
    console.log('🔗 用户触发重连...')
    if (pageState.status === 'ready' && user && organization) {
      connect({
        userId: user.id || 'anonymous',
        groupChatId: pageState.groupId,
        organizationId: organization.id || 'default'
      }).catch(error => {
        console.error('重连失败:', error)
      })
    }
  }, [connect, pageState, user, organization])

  // 🔧 性能优化：缓存占位符文本
  const placeholderText = useMemo(() => {
    if (hasActiveSession && currentSession) {
      return t('interface.placeholder.group', { groupId: currentSession.groupChatId })
    }
    return t('interface.placeholder.default')
  }, [hasActiveSession, currentSession, t])

  // 非阻塞连接状态显示组件 - 必须在条件渲染前定义
  const ConnectionStatusBar = useCallback(() => {
    const showConnectionStatus = connectionStatus === 'connecting' || connectionStatus === 'error'
    if (!showConnectionStatus) return null
    
    return (
      <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {connectionStatus === 'connecting' && (
              <>
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm text-blue-700">{t('status.connecting')}</span>
              </>
            )}
            {connectionStatus === 'error' && (
              <>
                <div className="w-4 h-4 bg-red-500 rounded-full" />
                <span className="text-sm text-red-700">{connectionError || t('error.title')}</span>
              </>
            )}
          </div>
          {connectionStatus === 'error' && (
            <button
              onClick={handleReconnect}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {t('error.reconnect')}
            </button>
          )}
        </div>
      </div>
    )
  }, [connectionStatus, connectionError, t, handleReconnect])

  // 会话初始化覆盖层 - 必须在条件渲染前定义
  const InitializingOverlay = useCallback(() => {
    const isInitializing = !hasActiveSession && pageState.status === 'ready'
    if (!isInitializing) return null
    
    return (
      <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-10">
        <div className="text-center">
          <Loading />
          <p className="mt-2 text-sm text-gray-600">{t('status.validating')}</p>
        </div>
      </div>
    )
  }, [hasActiveSession, pageState.status, t])

  // 🔧 渲染逻辑 - 基于新架构状态
  if (pageState.status === 'loading') {
    return (
      <div className="flex items-center justify-center h-full">
        <Loading />
      </div>
    )
  }


  // 🎯 优化后的非阻塞渲染 - 大幅提升加载性能
  return (
    <div className="flex flex-col h-full relative">
      <ConnectionStatusBar />
      <InitializingOverlay />
      
      <Suspense fallback={
        <div className="flex-1 flex items-center justify-center">
          <Loading />
        </div>
      }>
        <ChatErrorBoundary
          onError={() => {
            // 静默处理错误
          }}
          maxRetries={3}
        >
          <div className="flex-1">
            <ChatContainer
              groupChatId={pageState.groupId}
              userId={user?.id || 'anonymous'}
              organizationId={organization?.id || 'default'}
              appearance={{
                showAvatar: true,
                showTimestamp: false,
                enableCopy: true
              }}
              features={{
                autoConnect: true,
                enableFileUpload: false,
                enableVirtualScroll: true  // 启用虚拟滚动
              }}
              className="h-full"
              onError={() => {
                // 静默处理错误
              }}
            />
          </div>
        </ChatErrorBoundary>
      </Suspense>
    </div>
  )
}
