'use client'

import poll from '@/assets/poll.svg'
import { AIChatInput } from '@/components/chat/ai-chat-input'
import { useSession } from '@/components/providers/session-provider'
import { Button } from '@/components/ui/button'
import { useAsyncError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler'
import { useHttp } from '@/hooks/useHttp'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react'

// 使用动态导入优化组件加载
import dynamic from 'next/dynamic'

// 懒加载重型组件
const PollStepIndicator = dynamic(() => import('@/components/poll/step-indicator'), {
  loading: () => <div className="h-8 bg-gray-100 rounded animate-pulse" />,
  ssr: false
})

const PollTagSelection = dynamic(() => import('@/components/poll/tag-selection'), {
  loading: () => <div className="h-16 bg-gray-100 rounded animate-pulse" />,
  ssr: false
})

// 缓存相关常量
const POLL_CACHE_KEY = 'poll_data'
const CACHE_DEBOUNCE_MS = 500
const CACHE_VERSION = '1.0'

// 类型定义
interface SurveyQuestion {
  title: string
  options: Record<string, string>
}

type SurveyQuestionsData = Record<string, SurveyQuestion>

interface SurveyAnswer {
  field: string
  options: string[]
  user_input: string | null
}

interface PollData {
  currentStepIndex: number
  answers: SurveyAnswer[]
  stepFields: string[]
  timestamp: number
  version: string
}

// 缓存工具类 - 使用Web Worker优化
class PollCacheManager {
  private static debounceTimers = new Map<string, NodeJS.Timeout>()

  static async saveToCache(data: PollData): Promise<void> {
    const key = POLL_CACHE_KEY
    
    // 清除之前的定时器
    if (this.debounceTimers.has(key)) {
      clearTimeout(this.debounceTimers.get(key)!)
    }

    // 防抖保存
    return new Promise((resolve) => {
      const timer = setTimeout(async () => {
        try {
          // 使用requestIdleCallback优化性能
          if ('requestIdleCallback' in window) {
            window.requestIdleCallback(() => {
              localStorage.setItem(key, JSON.stringify({ ...data, version: CACHE_VERSION }))
              resolve()
            })
          } else {
            localStorage.setItem(key, JSON.stringify({ ...data, version: CACHE_VERSION }))
            resolve()
          }
        } catch (error) {
          console.warn('缓存保存失败:', error)
          resolve()
        }
        this.debounceTimers.delete(key)
      }, CACHE_DEBOUNCE_MS)
      
      this.debounceTimers.set(key, timer)
    })
  }

  static loadFromCache(): PollData | null {
    try {
      const cached = localStorage.getItem(POLL_CACHE_KEY)
      if (!cached) return null

      const data = JSON.parse(cached)
      
      // 版本检查
      if (data.version !== CACHE_VERSION) {
        localStorage.removeItem(POLL_CACHE_KEY)
        return null
      }

      return data
    } catch (error) {
      console.warn('缓存加载失败:', error)
      localStorage.removeItem(POLL_CACHE_KEY)
      return null
    }
  }

  static clearCache(): void {
    localStorage.removeItem(POLL_CACHE_KEY)
  }
}

// 自定义Hook：异步数据加载
function usePollData() {
  const [surveyQuestions, setSurveyQuestions] = useState<SurveyQuestionsData | null>(null)
  const [pollData, setPollData] = useState<PollData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { get } = useHttp({ showLoading: false, enableAutoErrorToast: true })
  const { safeAsync } = useAsyncErrorHandler()

  // 缓存的工具函数
  const memoizedUtils = useMemo(() => ({
    createInitialPollData: (surveyData: SurveyQuestionsData): PollData => {
      const stepFields = Object.keys(surveyData)
      const answers = stepFields.map(field => ({
        field,
        options: [] as string[],
        user_input: null as string | null,
      }))

      return {
        currentStepIndex: 0,
        answers,
        stepFields,
        timestamp: Date.now(),
        version: CACHE_VERSION,
      }
    },

    isCacheCompatible: (cached: any, surveyData: SurveyQuestionsData): boolean => {
      if (!cached?.stepFields || !cached?.answers) return false
      
      const currentFields = Object.keys(surveyData)
      const cachedFields = cached.stepFields
      
      return currentFields.length === cachedFields.length && 
             currentFields.every(field => cachedFields.includes(field))
    }
  }), [])

  // 异步数据加载
  useEffect(() => {
    let isMounted = true

    const loadData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // 1. 首先尝试从缓存恢复基本结构（快速显示）
        const cachedData = PollCacheManager.loadFromCache()
        if (cachedData && isMounted) {
          setPollData(cachedData)
          setIsLoading(false)
        }

        // 2. 异步获取最新数据
        const result = await safeAsync(
          async () => get<SurveyQuestionsData>('/ovs_profile/survey_questions'),
          {
            customMessage: '获取问卷数据失败，请检查网络连接后重试',
            criticalError: false,
          }
        )

        if (result?.success && result.data && isMounted) {
          setSurveyQuestions(result.data)

          // 3. 验证缓存兼容性并更新数据
          if (cachedData && memoizedUtils.isCacheCompatible(cachedData, result.data)) {
            // 缓存兼容，保持用户数据
            console.log('✅ 缓存数据兼容，保持用户进度')
          } else {
            // 缓存不兼容或无缓存，创建新数据
            console.log('🔄 创建新的问卷数据结构')
            const newData = memoizedUtils.createInitialPollData(result.data)
            setPollData(newData)
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : '数据加载失败')
        }
      } finally {
        if (isMounted) {
          setIsLoading(false)
        }
      }
      
      return // 显式返回语句
    }

    loadData()

    return () => {
      isMounted = false
    }
  }, [get, safeAsync, memoizedUtils])

  // 优化的数据保存
  const savePollData = useCallback(async (newData: PollData) => {
    setPollData(newData)
    await PollCacheManager.saveToCache(newData)
  }, [])

  return {
    surveyQuestions,
    pollData,
    isLoading,
    error,
    savePollData,
    utils: memoizedUtils
  }
}

// 主组件
export default function PollPageOptimized() {
  const router = useRouter()
  const { user, organization } = useSession()
  const t = useTranslations('poll')
  const tCommon = useTranslations('common')
  const inputRef = useRef<HTMLTextAreaElement>(null)

  const { surveyQuestions, pollData, isLoading, error, savePollData } = usePollData()
  const { post } = useHttp({ showLoading: false, enableAutoErrorToast: true })
  const { safeAsync } = useAsyncErrorHandler()

  // 缓存计算结果
  const computedData = useMemo(() => {
    if (!pollData || !surveyQuestions) {
      return {
        currentStepField: '',
        totalSteps: 0,
        currentStepTitle: '',
        currentStepOptions: [],
        currentAnswers: [],
        currentUserInput: '',
        canProceed: false,
        isLastStep: false
      }
    }

    const currentStepField = pollData.stepFields[pollData.currentStepIndex] || ''
    const totalSteps = pollData.stepFields.length
    const currentQuestion = surveyQuestions[currentStepField]
    const currentAnswer = pollData.answers[pollData.currentStepIndex]
    
    return {
      currentStepField,
      totalSteps,
      currentStepTitle: currentQuestion?.title || t(`steps.${currentStepField}.title`) || '',
      currentStepOptions: currentQuestion ? Object.keys(currentQuestion.options) : [],
      currentAnswers: currentAnswer?.options || [],
      currentUserInput: currentAnswer?.user_input || '',
      canProceed: (currentAnswer?.options.length || 0) > 0 || !!(currentAnswer?.user_input && currentAnswer.user_input.trim()),
      isLastStep: pollData.currentStepIndex === totalSteps - 1
    }
  }, [pollData, surveyQuestions, t])

  // 优化的事件处理函数
  const handlers = useMemo(() => ({
    handleTagClick: async (tag: string) => {
      if (!pollData) return

      const currentAnswers = computedData.currentAnswers
      const newAnswers = currentAnswers.includes(tag)
        ? currentAnswers.filter(t => t !== tag)
        : [...currentAnswers, tag]

      const newData = {
        ...pollData,
        answers: pollData.answers.map((answer, index) =>
          index === pollData.currentStepIndex
            ? { ...answer, options: newAnswers }
            : answer
        ),
        timestamp: Date.now(),
      }

      await savePollData(newData)
    },

    handleUserInputChange: async (value: string) => {
      if (!pollData) return

      const newData = {
        ...pollData,
        answers: pollData.answers.map((answer, index) =>
          index === pollData.currentStepIndex
            ? { ...answer, user_input: value }
            : answer
        ),
        timestamp: Date.now(),
      }

      await savePollData(newData)
    },

    handleNext: async () => {
      if (!pollData || pollData.currentStepIndex >= computedData.totalSteps - 1) return

      const newData = {
        ...pollData,
        currentStepIndex: pollData.currentStepIndex + 1,
        timestamp: Date.now(),
      }

      await savePollData(newData)
    },

    handlePrevious: async () => {
      if (!pollData) return

      if (pollData.currentStepIndex > 0) {
        const newData = {
          ...pollData,
          currentStepIndex: pollData.currentStepIndex - 1,
          timestamp: Date.now(),
        }
        await savePollData(newData)
      } else {
        router.back()
      }
    },

    handleSubmit: async () => {
      if (!pollData) return

      const result = await safeAsync(
        async () => post('/ovs_profile/submit_survey', { answers: pollData.answers }),
        {
          customMessage: '问卷提交失败，请重试',
          criticalError: false,
        }
      )

      if (result?.success) {
        PollCacheManager.clearCache()
        router.push('/chat')
      }
    }
  }), [pollData, computedData, savePollData, router, post, safeAsync])

  // 权限检查优化 - 非阻塞式
  useEffect(() => {
    if (!user) {
      router.push('/login')
      return
    }

    if (organization?.hasCompanyProfile === true) {
      router.push('/chat')
      return
    }
  }, [user, organization?.hasCompanyProfile, router])

  // 自动聚焦优化
  useEffect(() => {
    if (!isLoading && inputRef.current) {
      const timer = setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [pollData?.currentStepIndex, isLoading])

  // 加载状态
  if (isLoading || !surveyQuestions || !pollData) {
    return (
      <div className="h-full overflow-hidden bg-gray-50 flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-2xl p-8 flex flex-col items-center h-full justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">{t('loading')}</p>
        </div>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className="h-full overflow-hidden bg-gray-50 flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-2xl p-8 flex flex-col items-center h-full justify-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            {tCommon('retry')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full overflow-hidden bg-gray-50 flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-2xl p-8 flex flex-col items-center h-full overflow-auto">
        {/* 标题 */}
        <h1 className="text-2xl font-semibold text-gray-900 mb-3">{t('title')}</h1>

        {/* 卡通图标 - 延迟加载 */}
        <div className="mb-6">
          <Image 
            src={poll} 
            alt="poll" 
            width={250} 
            height={245}
            priority={false}
            loading="lazy"
          />
        </div>

        {/* 步骤指示器 - 懒加载 */}
        <Suspense fallback={<div className="h-8 bg-gray-100 rounded animate-pulse mb-6" />}>
          <PollStepIndicator 
            currentStep={pollData.currentStepIndex}
            totalSteps={computedData.totalSteps}
            className="mb-6"
          />
        </Suspense>

        {/* 当前步骤标题 */}
        <h2 className="text-lg font-medium text-gray-700 mb-2">
          {computedData.currentStepTitle}
        </h2>

        {/* 步骤进度 */}
        <p className="text-sm text-gray-500 mb-8 text-center">
          {t('step', {
            step: pollData.currentStepIndex + 1,
            total: computedData.totalSteps,
          })}
        </p>

        {/* 标签选择 - 懒加载 */}
        <Suspense fallback={<div className="h-16 bg-gray-100 rounded animate-pulse mb-6" />}>
          <PollTagSelection
            options={computedData.currentStepOptions}
            selectedOptions={computedData.currentAnswers}
            onTagClick={handlers.handleTagClick}
            surveyQuestions={surveyQuestions}
            currentField={computedData.currentStepField}
            className="mb-6"
          />
        </Suspense>

        {/* 用户输入框 */}
        <div className="w-full mb-8">
          <div className="mb-2 text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
            💡 您可以选择上方的选项，或者在下方输入文本，或者两者都选择
          </div>
          <AIChatInput
            value={computedData.currentUserInput}
            onChange={handlers.handleUserInputChange}
            onSendMessage={async () => {
              if (computedData.isLastStep) {
                await handlers.handleSubmit()
              }
            }}
            placeholder={t(`steps.${computedData.currentStepField}.userInputPlaceholder`) || '请输入补充信息...'}
            className="w-full"
            autoFocus={true}
            // Poll场景配置：不显示任何动作按钮
            showAttachButton={false}
            showVoiceButton={false}
            // 使用简单的容器样式
          />
        </div>

        {/* 底部按钮 */}
        <div className="flex gap-4 w-full justify-between">
          <Button 
            variant="outline" 
            onClick={handlers.handlePrevious} 
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            {tCommon('previous')}
          </Button>

          {computedData.isLastStep ? (
            <Button 
              onClick={handlers.handleSubmit} 
              disabled={!computedData.canProceed} 
              className="px-8"
            >
              {t('start')}
            </Button>
          ) : (
            <Button
              onClick={handlers.handleNext}
              disabled={!computedData.canProceed}
              className="px-8 flex items-center gap-2"
            >
              {tCommon('next')}
              <ArrowRight size={16} />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}