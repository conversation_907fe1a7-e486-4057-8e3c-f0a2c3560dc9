'use client'

import { useChatSecurity } from '@/hooks/use-chat-security'
import { useChatConnection, useChatStore } from '@/stores/chat-store'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useRef, useState } from 'react'

// 🔧 聊天连接状态类型
export type ChatConnectionState = 
  | { status: 'idle' }
  | { status: 'initializing' }
  | { status: 'connecting' }
  | { status: 'connected'; sessionId: string; groupId: string }
  | { status: 'error'; error: string; errorCode?: string }
  | { status: 'disconnected' }

// 🔧 连接配置选项
export interface ChatConnectionConfig {
  timeout?: number
  retryAttempts?: number
  retryDelay?: number
}

// 🔧 连接管理器选项
export interface ConnectionManagerOptions {
  groupId: string
  connectionConfig?: ChatConnectionConfig
  onStateChange?: (state: ChatConnectionState) => void
  onError?: (error: string, errorCode?: string) => void
}

/**
 * 聊天连接管理 Hook
 * 专门负责聊天系统的连接初始化、状态管理和错误处理
 * 分离连接逻辑，提供可复用的连接管理功能
 */
export function useChatConnectionManager(options: ConnectionManagerOptions) {
  const { groupId, connectionConfig, onStateChange, onError } = options
  
  // 🌐 国际化
  const t = useTranslations('chat')
  
  // 🔒 安全验证
  const { validateChatAccess, getSecurityErrorKey, currentUser } = useChatSecurity()
  
  // 📦 Store 和连接
  const ensureSessionForGroup = useChatStore(useCallback((state) => state.ensureSessionForGroup, []))
  const addMessage = useChatStore(useCallback((state) => state.addMessage, []))
  const { connect, disconnect } = useChatConnection()
  
  // 📊 状态管理
  const [connectionState, setConnectionState] = useState<ChatConnectionState>({ status: 'idle' })
  const abortControllerRef = useRef<AbortController | null>(null)
  const mountedRef = useRef(true)
  
  // 🔧 状态变更通知
  const notifyStateChange = useCallback((newState: ChatConnectionState) => {
    setConnectionState(newState)
    onStateChange?.(newState)
  }, [onStateChange])
  
  // 🔧 错误处理
  const handleError = useCallback((error: string, errorCode?: string) => {
    const errorState: ChatConnectionState = { 
      status: 'error', 
      error, 
      ...(errorCode && { errorCode })
    }
    notifyStateChange(errorState)
    
    // 添加错误消息到聊天界面
    addMessage({
      id: `error_${Date.now()}`,
      groupChatId: groupId,
      sessionId: ensureSessionForGroup(groupId, currentUser?.id || '', currentUser?.organizationId || '').sessionId,
      userId: currentUser?.id || '',
      organizationId: currentUser?.organizationId || '',
      payload: {
        type: 'error',
        code: errorCode || 'UNKNOWN_ERROR',
        message: error,
      },
      timestamp: new Date().toISOString(),
      status: 'failed',
    })
    
    onError?.(error, errorCode)
  }, [notifyStateChange, addMessage, onError, currentUser?.id, currentUser?.organizationId, ensureSessionForGroup, groupId])
  
  // 🔧 清理函数
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
    disconnect()
  }, [disconnect])
  
  // 🔧 连接初始化主函数
  const initializeConnection = useCallback(async () => {
    let isCancelled = false
    
    try {
      // 创建新的 AbortController
      cleanup() // 清理之前的连接
      abortControllerRef.current = new AbortController()
      
      // 1. 安全验证
      notifyStateChange({ status: 'initializing' })
      
      const securityResult = validateChatAccess(groupId)
      if (!securityResult.isValid) {
        const errorKey = getSecurityErrorKey(securityResult)
        const errorMessage = t(errorKey)
        handleError(errorMessage, securityResult.errorCode)
        return false
      }
      
      if (isCancelled || !mountedRef.current) return false
      
      // 2. 创建会话
      console.log('🏠 INITIALIZING_CONNECTION:', { 
        groupId, 
        userId: currentUser?.id, 
        organizationId: currentUser?.organizationId 
      })
      
      const session = ensureSessionForGroup(
        groupId, 
        currentUser!.id, 
        currentUser!.organizationId!
      )
      
      if (isCancelled || !mountedRef.current) return false
      
      // 3. 建立连接
      notifyStateChange({ status: 'connecting' })
      
      console.log('🔗 开始连接初始化...')
      await connect({
        userId: currentUser!.id,
        groupChatId: groupId,
        organizationId: currentUser!.organizationId!
      })
      
      if (isCancelled || !mountedRef.current) return false
      
      // 4. 连接成功
      notifyStateChange({ 
        status: 'connected', 
        sessionId: session.sessionId,
        groupId 
      })
      
      console.log('✅ CONNECTION_ESTABLISHED:', {
        groupId,
        sessionId: session.sessionId,
        userId: currentUser!.id,
        organizationId: currentUser!.organizationId,
      })
      
      return true
      
    } catch (error) {
      if (isCancelled || !mountedRef.current) return false
      
      console.error('❌ 连接初始化失败:', error)
      
      // 区分不同类型的错误
      if (error instanceof Error && error.name === 'AbortError') {
        return false // 正常取消，不显示错误
      }
      
      const errorMessage = t('error.initializationFailed', { 
        error: error instanceof Error ? error.message : t('error.unknown')
      })
      
      handleError(errorMessage, 'CONNECTION_INIT_FAILED')
      return false
    }
    
    return () => {
      isCancelled = true
    }
  }, [
    groupId, 
    validateChatAccess, 
    getSecurityErrorKey, 
    currentUser, 
    ensureSessionForGroup, 
    connect, 
    notifyStateChange,
    handleError,
    cleanup,
    t
  ])
  
  // 🔧 手动重连
  const reconnect = useCallback(async () => {
    if (connectionState.status === 'connecting' || connectionState.status === 'initializing') {
      return // 避免重复连接
    }
    
    return await initializeConnection()
  }, [connectionState.status, initializeConnection])
  
  // 🔧 手动断开
  const disconnectConnection = useCallback(() => {
    cleanup()
    notifyStateChange({ status: 'disconnected' })
  }, [cleanup, notifyStateChange])
  
  // 🔧 检查连接状态
  const isConnected = connectionState.status === 'connected'
  const isConnecting = connectionState.status === 'connecting' || connectionState.status === 'initializing'
  const hasError = connectionState.status === 'error'
  
  // 🔧 组件卸载时清理
  useEffect(() => {
    mountedRef.current = true
    return () => {
      mountedRef.current = false
      cleanup()
    }
  }, [cleanup])
  
  return {
    // 状态
    connectionState,
    isConnected,
    isConnecting,
    hasError,
    
    // 操作
    initializeConnection,
    reconnect,
    disconnect: disconnectConnection,
    
    // 工具
    cleanup,
    
    // 当连接成功时的数据
    ...(connectionState.status === 'connected' && {
      sessionId: connectionState.sessionId,
      connectedGroupId: connectionState.groupId
    })
  }
}

// 导出的类型已在上方定义