{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "delete": "删除", "edit": "编辑", "back": "返回", "next": "下一步", "previous": "上一步", "submit": "提交", "close": "关闭", "search": "搜索", "filter": "筛选", "refresh": "刷新", "retry": "重试"}, "auth": {"login": {"title": "欢迎回来", "subtitle": "登录到您的 Vibe Coding 账户", "username": "用户名或邮箱", "usernamePlaceholder": "请输入用户名或邮箱", "password": "密码", "passwordPlaceholder": "请输入密码", "forgotPassword": "忘记密码？", "loginButton": "登录", "loggingIn": "登录中...", "orLoginWith": "或者使用第三方登录", "loginWithApple": "使用 Apple 登录", "loginWithGoogle": "使用 Google 登录", "loginWithFacebook": "使用 Facebook 登录", "noAccount": "还没有账号？", "registerNow": "立即注册", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "企业级AI应用前端架构", "feature1": "✨ Vue3 到 Next.js 完整迁移", "feature2": "🚀 现代化组件系统", "feature3": "🔐 完整的认证与权限管理", "feature4": "🎨 企业级UI设计系统", "termsText": "点击继续即表示您同意我们的", "termsOfService": "服务条款", "and": "和", "privacyPolicy": "隐私政策", "period": "。"}, "register": {"title": "创建账户", "subtitle": "注册您的新账户", "name": "姓名", "namePlaceholder": "请输入您的姓名", "email": "邮箱", "emailPlaceholder": "请输入您的邮箱地址", "password": "密码", "passwordPlaceholder": "请输入密码（至少6位）", "companyName": "公司名称", "companyNamePlaceholder": "请输入公司名称", "inviteCode": "邀请码", "inviteCodePlaceholder": "请输入邀请码", "createAccountButton": "创建账户", "creatingAccount": "创建账户中...", "accountCreated": "账户创建成功！", "alreadyHaveAccount": "已有账户？", "signIn": "立即登录", "or": "或者", "registerSuccess": "注册成功！正在跳转到主页...", "registering": "注册中..."}, "validation": {"usernameRequired": "请输入用户名或邮箱", "passwordRequired": "请输入密码", "emailFormat": "请输入有效的邮箱地址", "passwordLength": "密码至少需要6个字符", "nameLength": "姓名至少需要2个字符", "companyNameLength": "公司名称至少需要2个字符", "inviteCodeLength": "邀请码格式不正确", "loginError": "登录过程中发生异常，请稍后重试", "allFieldsRequired": "请填写所有必填项", "passwordMismatch": "两次输入的密码不一致", "registerError": "注册失败，请检查网络连接或联系管理员"}, "messages": {"loginSuccess": "登录成功", "welcomeBack": "欢迎回来，{username}！", "loginFailed": "登录失败", "loginError": "登录过程中发生错误", "featureInDevelopment": "功能开发中", "forgotPasswordInDevelopment": "忘记密码功能正在开发中", "registerInDevelopment": "注册功能正在开发中"}, "emailVerification": {"pending": {"title": "验证您的邮箱地址", "subtitle": "我们已向您的邮箱发送了验证邮件", "emailSentTo": "验证邮件已发送至", "nextSteps": "接下来怎么做？", "step1": "查收您的邮箱（包括垃圾邮件文件夹）", "step2": "点击邮件中的“验证邮箱”按钮", "step3": "完成验证后即可正常使用账户", "resendEmail": "重新发送邮件", "resendEmailCooldown": "重新发送邮件 ({seconds}s)", "resending": "发送中...", "backToLogin": "返回登录", "checkingStatus": "正在检查验证状态...", "noEmailReceived": "没有收到邮件？请检查垃圾邮件文件夹", "needHelp": "如需帮助，请", "contactSupport": "联系客服"}, "failed": {"title": "验证邮件发送失败", "subtitle": "向您的邮箱发送验证邮件时出现问题", "emailFailedTo": "向以下邮箱发送验证邮件时失败", "errorDetails": "错误详情：", "possibleReasons": "可能的原因", "reason1": "邮件服务暂时不可用", "reason2": "邮箱地址可能存在问题", "reason3": "网络连接不稳定", "reason4": "邮件服务器配置问题", "solutions": "解决方案", "solution1": "重新发送验证邮件获取新的验证链接", "solution2": "检查邮箱中是否有其他验证邮件", "solution3": "尝试直接登录（如果账户已验证）", "solution4": "使用其他邮箱重新注册", "retryEmail": "重新发送验证邮件", "backToRegister": "返回注册", "backToLogin": "返回登录", "troubleshooting": "如果问题持续存在，请尝试：", "troubleshoot1": "检查邮箱地址是否正确", "troubleshoot2": "使用其他邮箱地址重新注册", "troubleshoot3": "稍后再试"}, "processing": {"title": "正在验证您的邮箱...", "subtitle": "请稍候，这只需要几秒钟", "verifying": "正在验证您的邮箱...", "success": "验证成功！", "successMessage": "您的邮箱已成功验证，正在跳转...", "failed": "验证失败", "redirecting": "正在跳转到错误页面..."}, "success": {"title": "邮箱验证成功！", "subtitle": "欢迎加入 SpecificAI，您的账户已激活", "welcomeMessage": "现在您可以登录您的账户，开始体验 SpecificAI 的强大功能。", "features": "您可以体验：", "feature1": "智能AI对话和内容生成", "feature2": "团队协作和项目管理", "feature3": "个性化工作流程定制", "autoRedirect": "{seconds} 秒后自动跳转到登录页面", "cancelAutoRedirect": "取消", "loginNow": "立即登录", "goToDashboard": "直接进入控制台", "helpDocs": "帮助文档", "contactSupport": "联系客服"}, "verifyFailed": {"title": "验证链接无效", "subtitle": "验证链接可能已过期或已被使用", "invalidToken": "验证链接格式错误", "invalidTokenDesc": "验证链接可能被截断或损坏", "tokenExpired": "验证链接已过期", "tokenExpiredDesc": "验证链接的有效期为1小时", "tokenUsed": "验证链接已被使用", "tokenUsedDesc": "该验证链接已经被使用过", "defaultError": "验证失败", "defaultErrorDesc": "验证过程中出现未知错误", "possibleReasons": "可能的原因：", "reason1": "验证链接已过期（有效期1小时）", "reason2": "验证链接已被使用", "reason3": "链接格式不正确", "reason4": "网络连接问题", "solutions": "解决方案", "solution1": "重新发送验证邮件获取新的验证链接", "solution2": "检查邮箱中是否有其他验证邮件", "solution3": "尝试直接登录（如果账户已验证）", "solution4": "使用其他邮箱重新注册", "resendEmail": "重新发送验证邮件", "enterEmail": "邮箱地址", "enterEmailPlaceholder": "请输入您的邮箱地址", "tryLogin": "尝试登录", "backToRegister": "重新注册"}, "test": {"title": "邮箱验证流程测试", "subtitle": "测试和演示完整的邮箱验证注册流程", "testEmail": "测试邮箱地址", "testEmailPlaceholder": "输入测试邮箱地址", "testEmailDesc": "此邮箱地址将用于测试各个页面的显示效果", "pendingScenario": "邮箱验证等待页面", "pendingScenarioDesc": "模拟邮件发送成功后的等待状态", "failedScenario": "邮件发送失败页面", "failedScenarioDesc": "模拟邮件发送失败的情况", "processingScenario": "邮箱验证处理页面", "processingScenarioDesc": "模拟处理邮件中的验证链接", "successScenario": "验证成功页面", "successScenarioDesc": "模拟邮箱验证成功的状态", "verifyFailedScenario": "验证失败页面", "verifyFailedScenarioDesc": "模拟邮箱验证失败的情况", "testScenario": "测试此场景", "backToRegister": "返回注册页面", "instructions": "使用说明：", "instruction1": "点击上方的测试场景卡片来体验不同的页面状态", "instruction2": "修改测试邮箱地址来查看不同邮箱的显示效果", "instruction3": "每个页面都包含完整的用户交互和错误处理逻辑", "instruction4": "在实际环境中，这些页面会根据真实的API响应进行跳转"}}, "legacy": {"login": "登录", "logout": "退出登录", "pleaseLogin": "请先登录", "invalidCredentials": "用户名或密码错误", "accountLocked": "账户已被锁定，请联系管理员", "userDeleted": "该用户已被删除，请联系管理员"}}, "navigation": {"chat": "Cha<PERSON>", "uiGallery": "UI Gallery", "home": "首页", "dashboard": "仪表板", "settings": "设置"}, "chat": {"title": "Chat Interface", "subtitle": "Coming soon...", "welcome": "欢迎来到 SpecificAI！", "description": "您的AI助手已准备就绪。", "placeholder": "输入消息...", "send": "发送", "clear": "清空对话", "typing": "正在输入...", "security": {"error": {"noUser": "未找到用户信息，请重新登录", "noOrganization": "未找到组织信息，请联系管理员", "invalidGroupId": "无效的聊天组ID，请刷新页面重试", "unknown": "安全验证失败，未知错误"}}, "status": {"loading": "加载中...", "validating": "验证权限中...", "connecting": "正在连接..."}, "error": {"title": "发生错误", "reload": "重新加载", "initializationFailed": "聊天初始化失败：{error}", "unknown": "未知错误", "reconnect": "重新连接", "boundary": {"connection": {"title": "网络连接异常", "message": "聊天服务暂时无法连接，请检查网络后重试。"}, "security": {"title": "访问权限异常", "message": "您没有访问此聊天的权限，请重新登录。"}, "render": {"title": "界面显示异常", "message": "聊天界面出现显示问题，正在尝试恢复。"}, "unknown": {"title": "系统异常", "message": "聊天系统遇到未知错误，请稍后重试。"}, "technicalDetails": "技术详情", "retry": {"connection": "重新连接", "general": "重试"}, "reset": "重置", "refresh": "刷新页面", "maxRetriesReached": "已达到最大重试次数，请刷新页面或联系支持"}}, "interface": {"placeholder": {"group": "在群聊 {groupId} 中输入消息...", "default": "输入消息测试智能连接系统..."}}}, "ui": {"gallery": {"title": "UI 组件库", "description": "测试和调试所有UI组件的样式验证", "buttons": "按钮组件", "inputs": "输入组件", "cards": "卡片组件", "markdown": "Markdown 渲染器演示"}}, "uiGallery": {"title": "UI Component Gallery", "subtitle": "项目中可用的组件展示", "radixComponents": "Radix U<PERSON> 组件", "customComponents": "自定义组件", "componentAvailable": "组件可用"}, "errors": {"unauthorized": "未授权，请重新登录", "noPermission": "权限不足，无法访问", "userNotLogin": "用户未登录，请先登录", "userDeleted": "该用户已被删除，请联系管理员", "networkError": "网络连接失败，请检查网络设置", "serverError": "服务器内部错误", "notFound": "请求的资源不存在", "badRequest": "请求参数错误", "forbidden": "权限不足", "timeout": "请求超时", "unknown": "未知错误"}, "language": {"switch": "切换语言", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "当前语言"}, "user": {"profile": "个人资料", "account": "账户", "preferences": "偏好设置", "avatar": "头像", "name": "姓名", "email": "邮箱", "role": "角色", "lastLogin": "最后登录", "memberSince": "注册时间"}, "poll": {"title": "我想更了解您", "subtitle": "请填写公司基本信息，以便我们更好地为您提供服务", "placeholder": "请描述您的公司情况或选择上方标签...", "start": "开始", "step": "第 {step} 步，共 {total} 步", "loading": "正在加载问卷选项...", "steps": {"country": {"title": "请选择您的国家/地区", "options": {"vietnam": "越南", "japan": "日本", "korea": "韩国", "eastAsia": "东亚国家", "southeastAsia": "东南亚地区"}}, "industry": {"title": "请选择您的行业类型", "options": {"technology": "科技/互联网", "manufacturing": "制造业", "finance": "金融服务", "retail": "零售/电商", "other": "其他行业"}}, "focus": {"title": "请选择您关注的领域", "options": {"aiAutomation": "AI自动化", "digitalTransformation": "数字化转型", "dataAnalytics": "数据分析", "processOptimization": "流程优化", "other": "其他领域"}}, "companyInfo": {"title": "请描述您的公司情况", "placeholder": "请详细描述您的公司规模、业务模式、主要痛点等信息..."}}}, "reportPage": {"defaultTitle": "报告页面", "reportTitle": "嵌入式报告", "reportDescription": "这是一个简化的报告页面，展示iframe嵌入功能。", "parametersTitle": "URL参数"}, "resultsPage": {"pageTitle": "结果页面", "resultsTitle": "分析结果", "description": "这里显示分析结果和相关数据。", "parametersTitle": "URL参数", "sampleReport1": "示例报告1", "sampleReport2": "示例报告2"}, "table": {"title": "标题", "status": {"completed": "已完成", "pending": "待处理", "running": "运行中", "failed": "失败"}, "date": "日期", "noData": "暂无数据"}, "dashboardPage": {"welcomeTitle": "欢迎", "welcomeMessage": "欢迎，{name}！", "dashboardTitle": "嵌入式仪表板", "dashboardDescription": "这是一个简化的仪表板页面，展示iframe嵌入功能。", "parametersTitle": "URL参数", "initializing": "正在初始化分析..."}, "ErrorPage": {"title": "出现了一些问题", "subtitle": "应用程序遇到了意外错误，我们正在努力解决", "errorDetails": "错误详情", "errorDetailsDescription": "以下信息可能有助于技术支持团队诊断问题", "errorMessage": "错误消息", "unknownError": "未知错误", "errorId": "错误ID", "occurrenceTime": "发生时间", "retry": "重试", "backToHome": "返回首页", "refreshPage": "刷新页面", "troubleshootingTitle": "故障排除建议", "troubleshooting1": "刷新页面或稍后重试", "troubleshooting2": "检查网络连接是否正常", "troubleshooting3": "清除浏览器缓存和Cookie", "troubleshooting4": "尝试使用其他浏览器访问", "stillHavingIssuesTitle": "问题仍然存在？", "stillHavingIssuesDescription": "如果错误持续出现，请联系我们的技术支持团队。请提供上述错误详情以便快速诊断。", "sendReport": "发送错误报告", "sending": "发送中...", "reportSent": "报告已发送", "resend": "重新发送", "sendingReport": "正在发送错误报告...", "sendReportFailed": "发送错误报告时出现异常，请稍后重试。", "sendFailed": "发送失败，请稍后重试", "footerCode": "错误代码: 500 | 服务器内部错误", "footerMessage": "我们已经自动记录此错误，技术团队将尽快处理。"}, "AnalysisReportsTable": {"taskName": "任务名称", "newTitle": "新标题", "status": "状态", "progress": "进度", "createdAt": "创建时间", "updatedAt": "更新时间", "actions": "操作", "completed": "已完成", "running": "进行中", "failed": "已失败", "pending": "等待中", "cancelled": "已取消"}}