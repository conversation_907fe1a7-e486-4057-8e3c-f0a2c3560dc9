/**
 * 优化的Chat Hook - 性能优先的聊天状态管理
 * 主要优化：
 * 1. 延迟初始化，不阻塞页面渲染
 * 2. 合并多个hooks，减少重复订阅
 * 3. 智能重连，避免无效连接
 * 4. 内存优化，消息分页
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useChatStore } from '@/stores/chat-store'

export interface OptimizedChatHookReturn {
  // 连接状态
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'
  isConnected: boolean
  error: string | null
  
  // 会话数据
  currentSession: any
  messages: any[]
  hasActiveSession: boolean
  
  // 操作方法
  initializeChat: (params: { groupId: string; userId: string; organizationId: string }) => void
  sendMessage: (message: string) => Promise<void>
  sendInteraction: (step: string, selection: string) => Promise<void>
  reconnect: () => void
  disconnect: () => void
}

/**
 * 优化的Chat Hook
 * 设计原则：延迟加载、智能缓存、减少重渲染
 */
export function useOptimizedChat(): OptimizedChatHookReturn {
  const [isInitialized, setIsInitialized] = useState(false)
  const initParamsRef = useRef<{ groupId: string; userId: string; organizationId: string } | null>(null)
  
  // 选择性订阅store状态，避免不必要的重渲染
  const connectionStatus = useChatStore(state => state.connection.status)
  const connectionError = useChatStore(state => state.connection.error)
  const currentSessionId = useChatStore(state => state.currentSessionId)
  const sessions = useChatStore(state => state.sessions)
  const error = useChatStore(state => state.error)
  
  // Store actions
  const {
    connect,
    disconnect: storeDisconnect,
    createSession,
    setCurrentSession,
    sendMessage: storeSendMessage,
    sendInteraction: storeSendInteraction
  } = useChatStore()

  // 计算衍生状态 - 使用useMemo缓存
  const derivedState = useMemo(() => {
    const currentSession = currentSessionId ? sessions[currentSessionId] : null
    const isConnected = connectionStatus === 'connected'
    const hasActiveSession = !!currentSession

    // 消息分页优化 - 只显示最近100条消息
    const messages = currentSession?.messages.slice(-100) || []

    return {
      currentSession,
      isConnected,
      hasActiveSession,
      messages,
      finalError: connectionError || error
    }
  }, [connectionStatus, connectionError, currentSessionId, sessions, error])

  // 延迟初始化聊天 - 关键优化
  const initializeChat = useCallback((params: { groupId: string; userId: string; organizationId: string }) => {
    if (isInitialized && initParamsRef.current?.groupId === params.groupId) {
      console.log('🔄 聊天已初始化，跳过重复初始化')
      return
    }

    console.log('🚀 延迟初始化聊天系统:', params)
    initParamsRef.current = params
    setIsInitialized(true)

    // 使用微任务确保不阻塞主线程
    Promise.resolve().then(async () => {
      try {
        // 1. 创建或获取会话
        const sessionId = createSession(params.groupId, params.userId, params.organizationId)
        setCurrentSession(sessionId)
        
        console.log('📁 会话创建完成:', sessionId)

        // 2. 延迟Socket连接（给页面渲染让路）
        setTimeout(async () => {
          try {
            console.log('🔌 开始Socket连接...')
            await connect({
              userId: params.userId,
              groupChatId: params.groupId,
              organizationId: params.organizationId,
            })
            console.log('✅ Socket连接完成')
          } catch (error) {
            console.error('❌ Socket连接失败:', error)
          }
        }, 100) // 100ms延迟，让页面先渲染

      } catch (error) {
        console.error('❌ 聊天初始化失败:', error)
      }
    })
  }, [isInitialized, connect, createSession, setCurrentSession])

  // 智能重连 - 避免无效重连
  const reconnect = useCallback(() => {
    if (!initParamsRef.current) {
      console.warn('⚠️ 没有初始化参数，无法重连')
      return
    }

    console.log('🔄 智能重连...')
    const params = initParamsRef.current
    
    // 先断开现有连接
    storeDisconnect()
    
    // 延迟重连
    setTimeout(async () => {
      try {
        await connect({
          userId: params.userId,
          groupChatId: params.groupId,
          organizationId: params.organizationId,
        })
      } catch (error) {
        console.error('❌ 重连失败:', error)
      }
    }, 1000)
  }, [connect, storeDisconnect])

  // 优化的消息发送
  const sendMessage = useCallback(async (message: string) => {
    try {
      await storeSendMessage(message)
    } catch (error) {
      console.error('❌ 消息发送失败:', error)
      throw error
    }
  }, [storeSendMessage])

  // 优化的交互发送
  const sendInteraction = useCallback(async (step: string, selection: string) => {
    try {
      await storeSendInteraction(step, selection)
    } catch (error) {
      console.error('❌ 交互发送失败:', error)
      throw error
    }
  }, [storeSendInteraction])

  // 清理资源
  const disconnect = useCallback(() => {
    console.log('🔌 断开聊天连接')
    setIsInitialized(false)
    initParamsRef.current = null
    storeDisconnect()
  }, [storeDisconnect])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (isInitialized) {
        console.log('🧹 清理聊天资源')
        disconnect()
      }
    }
  }, [isInitialized, disconnect])

  return {
    // 连接状态
    connectionStatus,
    isConnected: derivedState.isConnected,
    error: derivedState.finalError,
    
    // 会话数据
    currentSession: derivedState.currentSession,
    messages: derivedState.messages,
    hasActiveSession: derivedState.hasActiveSession,
    
    // 操作方法
    initializeChat,
    sendMessage,
    sendInteraction,
    reconnect,
    disconnect,
  }
}

/**
 * 轻量级连接状态Hook - 仅用于状态监控
 */
export function useConnectionStatus() {
  return useChatStore(state => ({
    status: state.connection.status,
    error: state.connection.error,
    isConnected: state.connection.status === 'connected',
    reconnectAttempts: state.connection.reconnectAttempts,
  }))
}

/**
 * 消息Hook - 专门处理消息相关操作
 */
export function useOptimizedMessages() {
  const currentSessionId = useChatStore(state => state.currentSessionId)
  const sessions = useChatStore(state => state.sessions)
  const sendMessage = useChatStore(state => state.sendMessage)
  const sendInteraction = useChatStore(state => state.sendInteraction)

  // 分页消息 - 性能优化
  const messages = useMemo(() => {
    if (!currentSessionId) return []
    const session = sessions[currentSessionId]
    // 只返回最近100条消息，避免内存问题
    return session?.messages.slice(-100) || []
  }, [currentSessionId, sessions])

  return {
    messages,
    sendMessage,
    sendInteraction,
    hasMessages: messages.length > 0
  }
}