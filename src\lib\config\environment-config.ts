/**
 * 环境配置管理 - Socket.IO连接配置
 * 
 * 设计目标：
 * 1. 统一的Socket.IO连接配置管理
 * 2. 开发/生产环境的智能切换
 * 3. 类型安全的配置验证
 */

// 环境模式定义
export type EnvironmentMode = 'development' | 'production'

// Socket.IO连接配置接口
export interface SocketEnvironmentConfig {
  mode: EnvironmentMode
  
  // WebSocket连接配置
  websocket: {
    url: string
    reconnectInterval: number
    maxReconnectAttempts: number
    heartbeatInterval: number
    timeout: number
  }
  
  // 功能特性配置
  features: {
    enablePerformanceMonitoring: boolean
    enableDataFlowLogging: boolean
    enableDebugMode: boolean
  }
}

// 默认WebSocket配置
const DEFAULT_WEBSOCKET_CONFIG = {
  url: process.env.NEXT_PUBLIC_SOCKET_URL || 'ws://localhost:8000/ws',
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
  heartbeatInterval: 30000,
  timeout: 20000,
}

// 默认功能配置
const DEFAULT_FEATURES_CONFIG = {
  enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
  enableDataFlowLogging: process.env.NODE_ENV === 'development',
  enableDebugMode: process.env.NODE_ENV === 'development',
}

// 环境配置管理器
export class EnvironmentConfigManager {
  private static instance: EnvironmentConfigManager
  private currentConfig: SocketEnvironmentConfig

  private constructor() {
    const mode = this.determineEnvironmentMode()
    
    this.currentConfig = {
      mode,
      websocket: DEFAULT_WEBSOCKET_CONFIG,
      features: DEFAULT_FEATURES_CONFIG,
    }
    
    console.log(`🌍 Environment initialized: ${mode}`, {
      socketUrl: this.currentConfig.websocket.url,
      timestamp: new Date().toISOString(),
    })
  }

  public static getInstance(): EnvironmentConfigManager {
    if (!EnvironmentConfigManager.instance) {
      EnvironmentConfigManager.instance = new EnvironmentConfigManager()
    }
    return EnvironmentConfigManager.instance
  }

  /**
   * 确定环境模式
   */
  private determineEnvironmentMode(): EnvironmentMode {
    // 检查环境变量设置
    const nodeEnv = process.env.NODE_ENV
    
    if (nodeEnv === 'production') {
      console.log('🚀 Production environment detected')
      return 'production'
    } else {
      console.log('🔧 Development environment detected')
      return 'development'
    }
  }

  /**
   * 获取当前环境配置
   */
  public getConfig(): SocketEnvironmentConfig {
    return { ...this.currentConfig }
  }

  /**
   * 获取当前环境模式
   */
  public getMode(): EnvironmentMode {
    return this.currentConfig.mode
  }

  /**
   * 判断是否为开发模式
   */
  public isDevelopmentMode(): boolean {
    return this.currentConfig.mode === 'development'
  }

  /**
   * 判断是否为生产模式
   */
  public isProductionMode(): boolean {
    return this.currentConfig.mode === 'production'
  }

  /**
   * 获取WebSocket配置
   */
  public getWebSocketConfig() {
    return this.currentConfig.websocket
  }

  /**
   * 获取功能配置
   */
  public getFeaturesConfig() {
    return this.currentConfig.features
  }

  /**
   * 更新配置（深度合并）
   */
  public updateConfig(partialConfig: Partial<SocketEnvironmentConfig>): void {
    this.currentConfig = {
      ...this.currentConfig,
      ...partialConfig,
      websocket: partialConfig.websocket 
        ? { ...this.currentConfig.websocket, ...partialConfig.websocket }
        : this.currentConfig.websocket,
      features: partialConfig.features
        ? { ...this.currentConfig.features, ...partialConfig.features }
        : this.currentConfig.features,
    }

    console.log('🔧 配置已更新:', this.currentConfig)
  }

  /**
   * 获取用户友好的环境描述
   */
  public getEnvironmentDescription(): string {
    switch (this.currentConfig.mode) {
      case 'development':
        return '🔧 开发模式 - 连接开发服务器'
      case 'production':
        return '🚀 生产模式 - 连接生产服务器'
      default:
        return '❓ 未知模式'
    }
  }

  /**
   * 验证当前配置是否有效
   */
  public validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    const wsConfig = this.getWebSocketConfig()

    if (!wsConfig.url) {
      errors.push('WebSocket URL不能为空')
    }
    if (wsConfig.reconnectInterval < 1000) {
      errors.push('重连间隔不能小于1秒')
    }
    if (wsConfig.maxReconnectAttempts < 1) {
      errors.push('最大重连次数不能小于1')
    }
    if (wsConfig.timeout < 5000) {
      errors.push('连接超时时间不能小于5秒')
    }

    return {
      valid: errors.length === 0,
      errors,
    }
  }

  /**
   * 生成诊断报告
   */
  public generateDiagnosticReport() {
    return {
      mode: this.getMode(),
      description: this.getEnvironmentDescription(),
      config: this.getConfig(),
      validation: this.validateConfig(),
      environment: {
        nodeEnv: process.env.NODE_ENV || 'unknown',
        websocketUrl: process.env.NEXT_PUBLIC_SOCKET_URL,
      }
    }
  }
}

// 全局单例实例
export const environmentConfig = EnvironmentConfigManager.getInstance()

// Hook风格接口
export const useEnvironmentConfig = () => {
  return {
    mode: environmentConfig.getMode(),
    isDevelopmentMode: environmentConfig.isDevelopmentMode(),
    isProductionMode: environmentConfig.isProductionMode(),
    websocketConfig: environmentConfig.getWebSocketConfig(),
    featuresConfig: environmentConfig.getFeaturesConfig(),
    description: environmentConfig.getEnvironmentDescription(),
    updateConfig: environmentConfig.updateConfig.bind(environmentConfig),
    validateConfig: environmentConfig.validateConfig.bind(environmentConfig),
    diagnosticReport: environmentConfig.generateDiagnosticReport.bind(environmentConfig),
  }
}