/**
 * 邮箱验证成功页面 - 客户端组件
 * 处理成功后的用户引导和跳转
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { ArrowRight, Sparkles, Users, Zap } from 'lucide-react'

export function VerifySuccessClient() {
  const router = useRouter()
  const t = useTranslations('auth.verification')
  
  const [countdown, setCountdown] = useState(10)
  const [autoRedirect, setAutoRedirect] = useState(true)

  // 自动跳转倒计时
  useEffect(() => {
    if (!autoRedirect) return

    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      // 倒计时结束，跳转到登录页面
      router.push('/login')
    }
  }, [countdown, autoRedirect, router])

  const handleGoToLogin = () => {
    router.push('/login')
  }

  const handleGoToDashboard = () => {
    router.push('/chat')
  }

  const handleCancelAutoRedirect = () => {
    setAutoRedirect(false)
  }

  return (
    <div className="space-y-6">
      {/* 成功信息 */}
      <div className="bg-green-50 rounded-lg p-4">
        <h3 className="font-semibold text-green-900 mb-2 flex items-center">
          <Sparkles className="w-4 h-4 mr-2" />
          开始使用
        </h3>
        <p className="text-sm text-green-800">
          现在您可以登录您的账户，开始体验 SpecificAI 的强大功能。
        </p>
      </div>

      {/* 功能亮点 */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">您可以体验：</h4>
        <div className="space-y-2">
          <div className="flex items-start space-x-3 text-sm">
            <Zap className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <span className="text-gray-700">智能AI对话和内容生成</span>
          </div>
          <div className="flex items-start space-x-3 text-sm">
            <Users className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <span className="text-gray-700">团队协作和项目管理</span>
          </div>
          <div className="flex items-start space-x-3 text-sm">
            <Sparkles className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <span className="text-gray-700">个性化工作流程定制</span>
          </div>
        </div>
      </div>

      {/* 自动跳转提示 */}
      {autoRedirect && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-800">
                {countdown} 秒后自动跳转到登录页面
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancelAutoRedirect}
              className="text-blue-600 hover:text-blue-700"
            >
              取消
            </Button>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="space-y-3">
        <Button
          onClick={handleGoToLogin}
          className="w-full"
        >
          立即登录
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>

        <Button
          onClick={handleGoToDashboard}
          variant="outline"
          className="w-full"
        >
          直接进入控制台
        </Button>
      </div>

      {/* 帮助信息 */}
      <div className="text-center text-xs text-gray-500 space-y-1">
        <p>如果您在使用过程中遇到任何问题</p>
        <p>
          请查看我们的
          <Button
            variant="link"
            size="sm"
            className="text-xs p-0 h-auto text-blue-600 hover:text-blue-500 mx-1"
            onClick={() => window.open('/help', '_blank')}
          >
            帮助文档
          </Button>
          或
          <Button
            variant="link"
            size="sm"
            className="text-xs p-0 h-auto text-blue-600 hover:text-blue-500 ml-1"
            onClick={() => window.open('/support', '_blank')}
          >
            联系客服
          </Button>
        </p>
      </div>
    </div>
  )
}
