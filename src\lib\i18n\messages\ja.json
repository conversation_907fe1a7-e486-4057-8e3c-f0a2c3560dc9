{"common": {"loading": "読み込み中...", "error": "エラー", "success": "成功", "cancel": "キャンセル", "confirm": "確認", "save": "保存", "delete": "削除", "edit": "編集", "back": "戻る", "next": "次へ", "previous": "前へ", "submit": "送信", "close": "閉じる", "search": "検索", "filter": "フィルター", "refresh": "更新", "retry": "再試行"}, "auth": {"login": {"title": "おかえりなさい", "subtitle": "Vibe Codingアカウントにサインイン", "username": "ユーザー名またはメール", "usernamePlaceholder": "ユーザー名またはメールを入力", "password": "パスワード", "passwordPlaceholder": "パスワードを入力", "forgotPassword": "パスワードを忘れましたか？", "loginButton": "ログイン", "loggingIn": "ログイン中...", "orLoginWith": "または次でログイン", "loginWithApple": "Appleでログイン", "loginWithGoogle": "Googleでログイン", "loginWithFacebook": "Facebookでログイン", "noAccount": "アカウントをお持ちでないですか？", "registerNow": "今すぐ登録", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "エンタープライズAIアプリケーションフロントエンドアーキテクチャ", "feature1": "✨ Vue3からNext.jsへの完全移行", "feature2": "🚀 モダンコンポーネントシステム", "feature3": "🔐 完全な認証・権限管理", "feature4": "🎨 エンタープライズUIデザインシステム", "termsText": "続行することで、当社の", "termsOfService": "利用規約", "and": "および", "privacyPolicy": "プライバシーポリシー", "period": "に同意したものとみなされます。"}, "register": {"title": "アカウント作成", "subtitle": "新しいアカウントを登録", "name": "お名前", "namePlaceholder": "お名前を入力", "email": "メール", "emailPlaceholder": "メールアドレスを入力", "password": "パスワード", "passwordPlaceholder": "パスワードを入力（6文字以上）", "companyName": "会社名", "companyNamePlaceholder": "会社名を入力", "inviteCode": "招待コード", "inviteCodePlaceholder": "招待コードを入力", "createAccountButton": "アカウント作成", "creatingAccount": "アカウント作成中...", "accountCreated": "アカウント作成完了！", "alreadyHaveAccount": "既にアカウントをお持ちですか？", "signIn": "今すぐサインイン", "or": "または", "registerSuccess": "登録成功！ホームページに移動しています...", "registering": "登録中..."}, "validation": {"usernameRequired": "ユーザー名またはメールを入力してください", "passwordRequired": "パスワードを入力してください", "emailFormat": "有効なメールアドレスを入力してください", "passwordLength": "パスワードは6文字以上である必要があります", "nameLength": "お名前は2文字以上である必要があります", "companyNameLength": "会社名は2文字以上である必要があります", "inviteCodeLength": "招待コードの形式が正しくありません", "loginError": "ログイン中にエラーが発生しました。後でもう一度お試しください", "allFieldsRequired": "すべての必須項目を入力してください", "passwordMismatch": "パスワードが一致しません", "registerError": "登録に失敗しました。ネットワーク接続を確認するか、管理者にお問い合わせください"}, "messages": {"loginSuccess": "ログイン成功", "welcomeBack": "おかえりなさい、{username}さん！", "loginFailed": "ログイン失敗", "loginError": "ログイン中にエラーが発生しました", "featureInDevelopment": "機能開発中", "forgotPasswordInDevelopment": "パスワード忘れ機能は開発中です", "registerInDevelopment": "登録機能は開発中です"}, "emailVerification": {"pending": {"title": "メールアドレスを確認してください", "subtitle": "確認メールをお送りしました", "emailSentTo": "確認メールを送信しました", "nextSteps": "次の手順", "step1": "メールボックスを確認してください（迷惑メールフォルダも含む）", "step2": "メール内の「メール確認」ボタンをクリックしてください", "step3": "確認完了後、アカウントをご利用いただけます", "resendEmail": "メールを再送信", "resendEmailCooldown": "メールを再送信 ({seconds}秒)", "resending": "送信中...", "backToLogin": "ログインに戻る", "checkingStatus": "確認状態をチェック中...", "noEmailReceived": "メールが届きませんか？迷惑メールフォルダをご確認ください", "needHelp": "サポートが必要な場合は", "contactSupport": "お問い合わせください"}, "failed": {"title": "確認メール送信失敗", "subtitle": "確認メールの送信中に問題が発生しました", "emailFailedTo": "以下のメールアドレスへの確認メール送信に失敗しました", "errorDetails": "エラー詳細：", "possibleReasons": "考えられる原因", "reason1": "メールサービスが一時的に利用できません", "reason2": "メールアドレスに問題がある可能性があります", "reason3": "ネットワーク接続が不安定です", "reason4": "メールサーバーの設定に問題があります", "solutions": "解決方法", "solution1": "確認メールを再送信して新しい確認リンクを取得する", "solution2": "メールボックスに他の確認メールがないか確認する", "solution3": "直接ログインを試す（アカウントが既に確認済みの場合）", "solution4": "別のメールアドレスで再登録する", "retryEmail": "確認メールを再送信", "backToRegister": "登録に戻る", "backToLogin": "ログインに戻る", "troubleshooting": "問題が続く場合は、以下をお試しください：", "troubleshoot1": "メールアドレスが正しいか確認する", "troubleshoot2": "別のメールアドレスで再登録する", "troubleshoot3": "しばらく時間をおいて再試行する"}, "processing": {"title": "メールアドレスを確認中...", "subtitle": "少々お待ちください。数秒で完了します", "verifying": "メールアドレスを確認中...", "success": "確認成功！", "successMessage": "メールアドレスの確認が完了しました。リダイレクト中...", "failed": "確認失敗", "redirecting": "エラーページにリダイレクト中..."}, "success": {"title": "メール確認完了！", "subtitle": "SpecificAIへようこそ。アカウントが有効化されました", "welcomeMessage": "アカウントにログインして、SpecificAIの強力な機能をお試しください。", "features": "ご利用いただける機能：", "feature1": "インテリジェントAI対話とコンテンツ生成", "feature2": "チーム協力とプロジェクト管理", "feature3": "パーソナライズされたワークフローのカスタマイズ", "autoRedirect": "{seconds}秒後にログインページに自動リダイレクトします", "cancelAutoRedirect": "キャンセル", "loginNow": "今すぐログイン", "goToDashboard": "ダッシュボードに移動", "helpDocs": "ヘルプドキュメント", "contactSupport": "サポートに連絡"}, "verifyFailed": {"title": "確認リンクが無効です", "subtitle": "確認リンクが期限切れまたは使用済みの可能性があります", "invalidToken": "確認リンクの形式が正しくありません", "invalidTokenDesc": "確認リンクが切り詰められたか破損している可能性があります", "tokenExpired": "確認リンクが期限切れです", "tokenExpiredDesc": "確認リンクの有効期限は1時間です", "tokenUsed": "確認リンクは既に使用されています", "tokenUsedDesc": "この確認リンクは既に使用されています", "defaultError": "確認失敗", "defaultErrorDesc": "確認中に不明なエラーが発生しました", "possibleReasons": "考えられる原因：", "reason1": "確認リンクが期限切れです（有効期限1時間）", "reason2": "確認リンクが既に使用されています", "reason3": "リンクの形式が正しくありません", "reason4": "ネットワーク接続の問題", "solutions": "解決方法", "solution1": "確認メールを再送信して新しい確認リンクを取得する", "solution2": "メールボックスに他の確認メールがないか確認する", "solution3": "直接ログインを試す（アカウントが既に確認済みの場合）", "solution4": "別のメールアドレスで再登録する", "resendEmail": "確認メールを再送信", "enterEmail": "メールアドレス", "enterEmailPlaceholder": "メールアドレスを入力してください", "tryLogin": "ログインを試す", "backToRegister": "再登録"}, "test": {"title": "メール確認フローテスト", "subtitle": "完全なメール確認登録フローのテストとデモンストレーション", "testEmail": "テスト用メールアドレス", "testEmailPlaceholder": "テスト用メールアドレスを入力", "testEmailDesc": "このメールアドレスは各ページの表示効果をテストするために使用されます", "pendingScenario": "メール確認待機ページ", "pendingScenarioDesc": "メール送信成功後の待機状態をシミュレート", "failedScenario": "メール送信失敗ページ", "failedScenarioDesc": "メール送信失敗の状況をシミュレート", "processingScenario": "メール確認処理ページ", "processingScenarioDesc": "メール内の確認リンクの処理をシミュレート", "successScenario": "確認成功ページ", "successScenarioDesc": "メール確認成功の状態をシミュレート", "verifyFailedScenario": "確認失敗ページ", "verifyFailedScenarioDesc": "メール確認失敗の状況をシミュレート", "testScenario": "このシナリオをテスト", "backToRegister": "登録ページに戻る", "instructions": "使用方法：", "instruction1": "上記のテストシナリオカードをクリックして、異なるページ状態を体験してください", "instruction2": "テスト用メールアドレスを変更して、異なるメールの表示効果を確認してください", "instruction3": "各ページには完全なユーザーインタラクションとエラー処理ロジックが含まれています", "instruction4": "実際の環境では、これらのページは実際のAPIレスポンスに基づいてリダイレクトされます"}}, "legacy": {"login": "ログイン", "logout": "ログアウト", "pleaseLogin": "まずログインしてください", "invalidCredentials": "ユーザー名またはパスワードが正しくありません", "accountLocked": "アカウントがロックされています。管理者にお問い合わせください", "userDeleted": "このユーザーは削除されています。管理者にお問い合わせください"}}, "navigation": {"chat": "チャット", "uiGallery": "UIギャラリー", "home": "ホーム", "dashboard": "ダッシュボード", "settings": "設定"}, "chat": {"title": "チャットインターフェース", "subtitle": "近日公開予定...", "welcome": "SpecificAIへようこそ！", "description": "AIアシスタントがお手伝いする準備ができています。", "placeholder": "メッセージを入力...", "send": "送信", "clear": "チャットをクリア", "typing": "入力中...", "security": {"error": {"noUser": "ユーザー情報が見つかりません。再度ログインしてください", "noOrganization": "組織情報が見つかりません。管理者にお問い合わせください", "invalidGroupId": "無効なチャットグループIDです。ページを更新して再試行してください", "unknown": "セキュリティ検証に失敗しました。未知のエラーです"}}, "status": {"loading": "読み込み中...", "validating": "権限を検証中...", "connecting": "接続中..."}, "error": {"title": "エラーが発生しました", "reload": "再読み込み", "initializationFailed": "チャット初期化に失敗しました：{error}", "unknown": "不明なエラー", "reconnect": "再接続", "boundary": {"connection": {"title": "ネットワーク接続異常", "message": "チャットサービスに一時的に接続できません。ネットワークを確認して再試行してください。"}, "security": {"title": "アクセス権限異常", "message": "このチャットにアクセスする権限がありません。再度ログインしてください。"}, "render": {"title": "インターフェース表示異常", "message": "チャットインターフェースで表示の問題が発生しました。復旧を試行しています。"}, "unknown": {"title": "システム異常", "message": "チャットシステムで未知のエラーが発生しました。後でもう一度お試しください。"}, "technicalDetails": "技術詳細", "retry": {"connection": "再接続", "general": "再試行"}, "reset": "リセット", "refresh": "ページを更新", "maxRetriesReached": "最大再試行回数に達しました。ページを更新するかサポートにお問い合わせください。"}}, "interface": {"placeholder": {"group": "グループ {groupId} でメッセージを入力...", "default": "インテリジェント接続システムをテストするためにメッセージを入力..."}}}, "ui": {"gallery": {"title": "UIコンポーネントギャラリー", "description": "スタイル検証のためのすべてのUIコンポーネントのテストとデバッグ", "buttons": "ボタンコンポーネント", "inputs": "入力コンポーネント", "cards": "カードコンポーネント", "markdown": "Markdownレンダラーデモ"}}, "uiGallery": {"title": "UIコンポーネントギャラリー", "subtitle": "プロジェクトで利用可能なコンポーネントの展示", "radixComponents": "Radi<PERSON> UIコンポーネント", "customComponents": "カスタムコンポーネント", "componentAvailable": "コンポーネント利用可能"}, "errors": {"unauthorized": "認証されていません。再度ログインしてください", "noPermission": "アクセス権限が不足しています", "userNotLogin": "ユーザーがログインしていません。まずログインしてください", "userDeleted": "このユーザーは削除されています。管理者にお問い合わせください", "networkError": "ネットワーク接続に失敗しました。ネットワーク設定を確認してください", "serverError": "サーバー内部エラー", "notFound": "要求されたリソースが見つかりません", "badRequest": "リクエストパラメータが正しくありません", "forbidden": "権限が不足しています", "timeout": "リクエストタイムアウト", "unknown": "未知のエラー"}, "language": {"switch": "言語切り替え", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "現在の言語"}, "user": {"profile": "プロフィール", "account": "アカウント", "preferences": "設定", "avatar": "アバター", "name": "名前", "email": "メール", "role": "役割", "lastLogin": "最終ログイン", "memberSince": "登録日"}, "poll": {"title": "あなたの会社について教えてください", "subtitle": "より良いサービスを提供するため、会社情報をご記入ください", "placeholder": "会社の状況を説明するか、上記のタグを選択してください...", "start": "開始", "step": "ステップ {step} / {total}", "loading": "アンケート項目を読み込み中...", "steps": {"country": {"title": "国・地域を選択してください", "options": {"vietnam": "ベトナム", "japan": "日本", "korea": "韓国", "eastAsia": "東アジア諸国", "southeastAsia": "東南アジア地域"}}, "industry": {"title": "業界タイプを選択してください", "options": {"technology": "テクノロジー/インターネット", "manufacturing": "製造業", "finance": "金融サービス", "retail": "小売/Eコマース", "other": "その他の業界"}}, "focus": {"title": "関心のある分野を選択してください", "options": {"aiAutomation": "AI自動化", "digitalTransformation": "デジタル変革", "dataAnalytics": "データ分析", "processOptimization": "プロセス最適化", "other": "その他の分野"}}, "companyInfo": {"title": "会社について説明してください", "placeholder": "会社の規模、ビジネスモデル、主な課題などの詳細をご記入ください..."}}}, "embed": {"initializing": "初期化中...", "loading": "ページを読み込んでいます", "pageNotFound": "要求されたページ '{page}' が見つかりませんでした。", "paramError": "パラメータエラーのため、ページの読み込みに失敗しました。", "errorTitle": "エラーが発生しました"}, "biddingChat": {"viewFullReport": "完全なレポートを表示", "generatingReport": "最終レポートを生成中...", "showProcess": "プロセスを表示", "hideProcess": "プロセスを非表示", "scrollToBottom": "一番下にスクロール"}, "reportPage": {"title": "分析", "header": "分析レポート", "queryId": "クエリID: {queryId}", "loading": "読み込み中...", "complete": "完了", "errorTitle": "レポートの読み込み中にエラーが発生しました:", "missingParams": "レポートの取得に必要なパラメータがありません。", "unknownError": "不明なエラーが発生しました", "summaryTitle": "分析概要", "backgroundTitle": "背景分析", "noData": "レポートデータがありません"}, "dashboardPage": {"errorTitle": "エラー", "missingTenderId": "URLパラメータに入札IDが見つかりません。有効な入札IDを入力してください。", "liveAnalysisTitle": "リアルタイム分析プロセス", "liveAnalysisDescription": "リアルタイム分析プロセスを表示しています。完了すると、完全なレポートを表示するボタンが表示されます。", "scrollToBottom": "一番下にスクロール", "initializing": "分析を初期化中..."}, "ErrorPage": {"title": "問題が発生しました", "subtitle": "アプリケーションで予期せぬエラーが発生しました。現在、解決に取り組んでいます。", "errorDetails": "エラー詳細", "errorDetailsDescription": "以下の情報は、サポートチームが問題を診断するのに役立つ可能性があります。", "errorMessage": "エラーメッセージ", "unknownError": "不明なエラー", "errorId": "エラーID", "occurrenceTime": "発生時刻", "retry": "再試行", "backToHome": "ホームに戻る", "refreshPage": "ページを更新", "troubleshootingTitle": "トラブルシューティングの提案", "troubleshooting1": "ページを更新するか、後でもう一度お試しください。", "troubleshooting2": "ネットワーク接続が安定しているか確認してください。", "troubleshooting3": "ブラウザのキャッシュとCookieをクリアしてください。", "troubleshooting4": "別のブラウザでサイトにアクセスしてみてください。", "stillHavingIssuesTitle": "まだ問題が解決しませんか？", "stillHavingIssuesDescription": "エラーが解決しない場合は、テクニカルサポートチームにお問い合わせください。迅速な診断のために、上記のエラー詳細を提供してください。", "sendReport": "エラー報告を送信", "sending": "送信中...", "reportSent": "報告が送信されました", "resend": "再送信", "sendingReport": "エラー報告を送信しています...", "sendReportFailed": "エラー報告の送信中に例外が発生しました。後でもう一度お試しください。", "sendFailed": "送信に失敗しました。後でもう一度お試しください。", "footerCode": "エラーコード: 500 | 内部サーバーエラー", "footerMessage": "このエラーは自動的に記録されており、技術チームができるだけ早く対応します。"}, "AnalysisReportsTable": {"taskName": "タスク名", "newTitle": "新しいタイトル", "status": "ステータス", "progress": "進捗", "createdAt": "作成日時", "updatedAt": "更新日時", "actions": "アクション", "completed": "完了", "running": "実行中", "failed": "失敗", "pending": "保留中", "cancelled": "キャンセル済み"}}