/**
 * 邮箱验证失败页面
 * 显示验证失败状态并提供解决方案
 */

import { VerifyFailedClient } from '@/app/(auth)/verify-failed/verify-failed-client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle } from 'lucide-react'
import { Suspense } from 'react'

// 服务端组件 - 处理页面结构和SEO
export default function VerifyFailedPage() {
  return (
    <div className="min-h-svh flex flex-col items-center justify-center p-4 sm:p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card className="shadow-lg border-red-200">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              验证链接无效
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              验证链接可能已过期或已被使用
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Suspense fallback={<VerifyFailedFallback />}>
              <VerifyFailedClient />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 加载状态组件
function VerifyFailedFallback() {
  return (
    <div className="space-y-6">
      <div className="bg-red-50 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-red-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-red-200 rounded w-full mb-1"></div>
          <div className="h-3 bg-red-200 rounded w-5/6 mb-1"></div>
          <div className="h-3 bg-red-200 rounded w-4/5"></div>
        </div>
      </div>
      
      <div className="space-y-3">
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  )
}

// 页面元数据
export const metadata = {
  title: '验证失败 - SpecificAI',
  description: '邮箱验证失败，请重新发送验证邮件或联系客服',
}
