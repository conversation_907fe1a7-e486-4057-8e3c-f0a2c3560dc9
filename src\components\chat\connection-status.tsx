/**
 * 连接状态组件 - 显示Socket.IO连接状态
 * 基于新的chat-store.ts架构重构
 */

'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Wifi, WifiOff, Loader2, AlertCircle } from 'lucide-react'

// UI组件
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface ConnectionStatusProps {
  /** 连接状态 */
  status: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'
  /** 错误信息 */
  error?: string | null
  /** 自定义样式 */
  className?: string
  /** 重连回调 */
  onReconnect?: () => void
}

// ============================================================================
// 状态配置
// ============================================================================

const STATUS_CONFIG = {
  disconnected: {
    icon: WifiOff,
    label: '未连接',
    variant: 'secondary' as const,
    color: 'text-muted-foreground',
  },
  connecting: {
    icon: Loader2,
    label: '连接中...',
    variant: 'secondary' as const,
    color: 'text-blue-500',
    animate: true,
  },
  connected: {
    icon: Wifi,
    label: '已连接',
    variant: 'default' as const,
    color: 'text-green-500',
  },
  reconnecting: {
    icon: Loader2,
    label: '重连中...',
    variant: 'secondary' as const,
    color: 'text-yellow-500',
    animate: true,
  },
  error: {
    icon: AlertCircle,
    label: '连接错误',
    variant: 'destructive' as const,
    color: 'text-red-500',
  },
}

// ============================================================================
// 连接状态组件
// ============================================================================

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  status,
  error,
  className,
  onReconnect,
}) => {
  const config = STATUS_CONFIG[status]
  const Icon = config.icon

  // 如果连接正常且没有错误，不显示状态栏
  if (status === 'connected' && !error) {
    return null
  }

  return (
    <div className={cn(
      'flex items-center justify-between p-2 border-b bg-muted/50',
      className
    )}>
      <div className="flex items-center gap-2">
        <Icon 
          className={cn(
            'h-4 w-4',
            config.color,
            'animate' in config && config.animate && 'animate-spin'
          )} 
        />
        <Badge variant={config.variant} className="text-xs">
          {config.label}
        </Badge>
        
        {/* 显示错误信息 */}
        {error && (
          <span className="text-xs text-destructive">
            {error}
          </span>
        )}
      </div>

      {/* 重连按钮 */}
      {(status === 'error' || status === 'disconnected') && onReconnect && (
        <Button
          variant="outline"
          size="sm"
          onClick={onReconnect}
          className="h-6 px-2 text-xs"
        >
          重新连接
        </Button>
      )}
    </div>
  )
}

export default ConnectionStatus