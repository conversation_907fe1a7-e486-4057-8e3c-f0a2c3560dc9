/**
 * 邮件发送失败页面
 * 处理邮件发送失败情况，提供重试和故障排除选项
 */

import { Suspense } from 'react'
import { EmailVerificationFailedClient } from './email-verification-failed-client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle } from 'lucide-react'

// 服务端组件 - 处理页面结构和SEO
export default function EmailVerificationFailedPage() {
  return (
    <div className="min-h-svh flex flex-col items-center justify-center p-4 sm:p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card className="shadow-lg border-red-200">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              验证邮件发送失败
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              向您的邮箱发送验证邮件时出现问题
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Suspense fallback={<EmailVerificationFailedFallback />}>
              <EmailVerificationFailedClient />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 加载状态组件
function EmailVerificationFailedFallback() {
  return (
    <div className="space-y-6">
      <div className="bg-yellow-50 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-yellow-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-yellow-200 rounded w-full mb-1"></div>
          <div className="h-3 bg-yellow-200 rounded w-5/6 mb-1"></div>
          <div className="h-3 bg-yellow-200 rounded w-4/5"></div>
        </div>
      </div>
      
      <div className="space-y-3">
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  )
}

// 页面元数据
export const metadata = {
  title: '邮件发送失败 - SpecificAI',
  description: '验证邮件发送失败，请重试或联系客服',
}
