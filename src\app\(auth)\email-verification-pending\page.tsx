/**
 * 邮箱验证等待页面
 * 显示验证邮件发送成功状态，提供重发功能和用户指引
 */

import { EmailVerificationPendingClient } from '@/app/(auth)/email-verification-pending/email-verification-pending-client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Mail } from 'lucide-react'
import { Suspense } from 'react'

// 服务端组件 - 处理页面结构和SEO
export default function EmailVerificationPendingPage() {
  return (
    <div className="min-h-svh flex flex-col items-center justify-center p-4 sm:p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Mail className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              验证您的邮箱地址
            </CardTitle>
            <CardDescription className="text-gray-600 mt-2">
              我们已向您的邮箱发送了验证邮件
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Suspense fallback={<EmailVerificationPendingFallback />}>
              <EmailVerificationPendingClient />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 加载状态组件
function EmailVerificationPendingFallback() {
  return (
    <div className="space-y-6">
      <div className="bg-blue-50 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-blue-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-blue-200 rounded w-full mb-1"></div>
          <div className="h-3 bg-blue-200 rounded w-5/6 mb-1"></div>
          <div className="h-3 bg-blue-200 rounded w-4/5"></div>
        </div>
      </div>
      
      <div className="space-y-3">
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  )
}

// 页面元数据
export const metadata = {
  title: '验证邮箱 - SpecificAI',
  description: '请查收您的邮箱并点击验证链接完成注册',
}
