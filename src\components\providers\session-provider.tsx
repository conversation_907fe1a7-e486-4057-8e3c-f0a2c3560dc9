'use client'

import React, {
  createContext,
  useContext,
  useMemo,
  useEffect,
  useCallback,
  type ReactNode,
} from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useBetterAuthSession, authClient } from '@/lib/auth/auth-client'
import { getUserLocale } from '@/lib/i18n/config'

// 组织信息类型定义
interface Organization {
  id: string
  hasCompanyProfile: boolean
  role: string
  [key: string]: any
}

// 扩展 Better Auth session 类型以包含 organization
interface ExtendedSessionData {
  user?: any
  session?: any
  organization?: Organization
}

interface SessionContextType {
  user: any | null
  session: any | null
  organization: Organization | null
  loading: boolean
  error: string | null
  isAuthenticated: boolean
  refetch: () => Promise<any>
  changeLanguage: (language: string) => Promise<void>
}

const SessionContext = createContext<SessionContextType | undefined>(undefined)

export function SessionProvider({ children }: { children: ReactNode }) {
  // 使用Better Auth官方的useSession Hook
  const { data: sessionData, isPending, error } = useBetterAuthSession()
  const router = useRouter()
  const pathname = usePathname()

  // 类型断言以包含 organization 字段
  const session = sessionData as ExtendedSessionData | null

  // 🎯 错误处理：将500等服务器错误视为未认证状态
  const isAuthError =
    error &&
    (error.message?.includes('500') ||
      error.message?.includes('Internal Server Error') ||
      error.message?.includes('Failed to fetch'))

  // 🔍 开发环境记录认证错误，但不影响用户体验
  useEffect(() => {
    if (isAuthError && process.env.NODE_ENV === 'development') {
      console.warn('🚫 Better Auth认证错误，将作为未认证处理:', {
        error: error?.message,
        timestamp: new Date().toISOString(),
        action: '用户将被重定向到登录页面',
      })
    }
  }, [isAuthError, error])

  // 🎯 检查用户是否为管理员
  const isAdminUser = (user: any): boolean => {
    const userRole = user?.role
    return userRole === 'admin' || userRole === 'super_admin'
  }

  // 🎯 公司资料检查和重定向逻辑
  useEffect(() => {
    // 只在客户端执行，避免 SSR 问题
    if (typeof window === 'undefined') return

    // 检查条件：用户已认证、数据已加载、不在错误状态
    if (!isAuthError && !isPending && session?.user && session?.organization) {
      const user = session.user
      const organization = session.organization as Organization

      // 🎯 管理员用户跳过公司资料检查
      if (isAdminUser(user)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔓 管理员用户，跳过公司资料检查')
        }
        return
      }

      // 检查是否需要填写公司资料
      if (organization.hasCompanyProfile === false) {
        // 避免在 /poll 页面重复重定向
        if (!pathname.includes('/poll')) {
          console.log('🔄 用户需要填写公司资料，重定向到问卷页面')
          router.push('/poll')
        }
      }
    }
  }, [session, isAuthError, isPending, pathname, router])

  // 设置用户语言cookie
  const setUserLocaleCookie = (userLanguage: string) => {
    const locale = getUserLocale(userLanguage)
    document.cookie = `user-locale=${locale}; path=/; max-age=${30 * 24 * 60 * 60}; samesite=lax`
    console.log(`🌍 设置用户语言cookie: ${userLanguage} → ${locale}`)
  }

  // 监听session中的用户语言变化
  useEffect(() => {
    const userWithLanguage = session?.user as any
    if (userWithLanguage?.language) {
      setUserLocaleCookie(userWithLanguage.language)
    }
  }, [session?.user])

  // 手动刷新session的方法
  const refetch = useCallback(async () => {
    try {
      console.log('🔄 手动刷新会话信息...')
      const result = await authClient.getSession()
      console.log('✅ 会话刷新成功:', result.data)
      return result.data
    } catch (err) {
      console.error('❌ 会话刷新失败:', err)
      throw err
    }
  }, [])

  // 切换语言方法
  const changeLanguage = useCallback(
    async (language: string) => {
      try {
        console.log('🔄 切换用户语言...', language)

        // 调用后端API更新用户语言设置
        const response = await fetch('/api/auth/user/language', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ language }),
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error('更新语言设置失败')
        }

        const result = await response.json()
        console.log('✅ 语言更新成功:', result)

        // 更新cookie
        setUserLocaleCookie(language)

        // 刷新session以获取最新的用户信息
        await refetch()

        // 刷新页面以应用新的语言设置
        if (typeof window !== 'undefined') {
          window.location.reload()
        }
      } catch (err) {
        console.error('❌ 语言切换失败:', err)
        throw err
      }
    },
    [refetch]
  )

  const contextValue = useMemo(
    () => ({
      user: isAuthError ? null : session?.user || null,
      session: isAuthError ? null : session?.session || null,
      organization: isAuthError ? null : session?.organization || null,
      loading: isPending,
      // 🎯 友好的错误信息：认证错误显示重新登录提示，其他错误保持原样
      error: isAuthError ? '请重新登录' : error?.message || null,
      isAuthenticated: !isAuthError && !!session?.user,
      refetch,
      changeLanguage,
    }),
    [session, isPending, error, isAuthError, refetch, changeLanguage]
  )

  return <SessionContext.Provider value={contextValue}>{children}</SessionContext.Provider>
}

export function useSession() {
  const context = useContext(SessionContext)
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider')
  }
  return context
}

// 导出类型
export type { SessionContextType }
