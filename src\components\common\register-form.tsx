'use client'

import { FrontendLanguageSwitch } from '@/components/common/frontend-language-switch'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

export function RegisterForm({ className, ...props }: React.ComponentProps<'div'>) {
  const router = useRouter()
  const t = useTranslations('auth.register')
  const tValidation = useTranslations('auth.validation')

  // 注册组件现在不需要使用 SessionProvider 的状态
  // const { loading: sessionLoading } = useSession()

  // 状态管理
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    company_name: '',
    invite_code: '',
  })

  // 移除了依赖认证状态的重定向逻辑，改为在注册成功后直接跳转

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
    // 清除错误信息
    if (error) setError('')
  }
  const checkValidate = () => {
    if (
      !formData.name ||
      !formData.email ||
      !formData.password ||
      !formData.company_name ||
      !formData.invite_code
    ) {
      setError(tValidation('allFieldsRequired'))
      return false
    }

    // 姓名验证
    if (formData.name.length < 2) {
      setError(tValidation('nameLength'))
      return false
    }

    // 邮箱格式验证
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setError(tValidation('emailFormat'))
      return false
    }

    // 密码强度验证
    if (formData.password.length < 6) {
      setError(tValidation('passwordLength'))
      return false
    }

    // 公司名称验证
    if (formData.company_name.length < 2) {
      setError(tValidation('companyNameLength'))
      return false
    }

    // 邀请码验证
    if (formData.invite_code.length < 6) {
      setError(tValidation('inviteCodeLength'))
      return false
    }
    return true
  }
  // 处理注册提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!checkValidate()) {
      return
    }

    setIsLoading(true)
    setError('')

    try {
      console.log('📝 开始注册流程:', formData.email)

      // 构造注册请求数据
      const registerData = {
        email: formData.email,
        password: formData.password,
        name: formData.name,
        company_name: formData.company_name,
        invite_code: formData.invite_code,
      }
      // 🔧 使用 axios 调用注册接口
      
      // 注册成功，直接跳转到 dashboard
      router.push(`/chat`)
    } catch (err: any) {
      console.error('💥 注册过程发生错误:', err)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignInClick = () => {
    console.log('🔗 点击登录按钮，跳转到登录页')
    router.push(`/login`)
  }

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">{t('title')}</CardTitle>
          <CardDescription>{t('subtitle')}</CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 sm:gap-6">
              {/* 错误消息显示 */}
              {error && (
                <div className="text-red-500 text-sm text-center bg-red-50 p-3 rounded-lg">
                  {error}
                </div>
              )}

              {/* 成功消息显示已移除 */}

              <div className="grid gap-4 sm:gap-6">
                <div className="grid gap-2 sm:gap-3">
                  <Label htmlFor="name">{t('name')}</Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    placeholder={t('namePlaceholder')}
                    value={formData.name}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                </div>

                <div className="grid gap-2 sm:gap-3">
                  <Label htmlFor="email">{t('email')}</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder={t('emailPlaceholder')}
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                </div>

                <div className="grid gap-2 sm:gap-3">
                  <Label htmlFor="password">{t('password')}</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder={t('passwordPlaceholder')}
                    value={formData.password}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                </div>

                <div className="grid gap-2 sm:gap-3">
                  <Label htmlFor="company_name">{t('companyName')}</Label>
                  <Input
                    id="company_name"
                    name="company_name"
                    type="text"
                    placeholder={t('companyNamePlaceholder')}
                    value={formData.company_name}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                </div>

                <div className="grid gap-2 sm:gap-3">
                  <Label htmlFor="invite_code">{t('inviteCode')}</Label>
                  <Input
                    id="invite_code"
                    name="invite_code"
                    type="text"
                    placeholder={t('inviteCodePlaceholder')}
                    value={formData.invite_code}
                    onChange={handleInputChange}
                    disabled={isLoading}
                    required
                  />
                </div>

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? t('creatingAccount') : t('createAccountButton')}
                </Button>
              </div>

              <div className="text-center text-sm">
                {t('alreadyHaveAccount')}{' '}
                <button
                  type="button"
                  onClick={handleSignInClick}
                  className="underline underline-offset-4"
                  disabled={isLoading}
                >
                  {t('signIn')}
                </button>
              </div>

              {/* 语言切换组件 */}
              <div className="flex justify-center mt-4 sm:mt-6 pt-4 sm:pt-6 border-t">
                <FrontendLanguageSwitch variant="ghost" size="sm" />
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
