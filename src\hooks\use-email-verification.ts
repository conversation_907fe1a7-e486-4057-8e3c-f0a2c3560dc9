/**
 * 邮箱验证状态管理Hook
 * 处理邮箱验证流程的状态和逻辑
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { AuthService, handleAuthError } from '@/lib/services/auth-service'
import type { 
  EmailVerificationStatus, 
  User, 
  RegistrationStep,
  EmailVerificationCheckResponse 
} from '@/types/auth'

interface UseEmailVerificationOptions {
  email?: string
  checkInterval?: number // 检查间隔（毫秒）
  autoCheck?: boolean // 是否自动检查验证状态
}

interface UseEmailVerificationReturn {
  // 状态
  verificationStatus: EmailVerificationStatus
  isResending: boolean
  isChecking: boolean
  error: string | null
  cooldownSeconds: number
  
  // 方法
  resendEmail: () => Promise<void>
  checkVerificationStatus: () => Promise<EmailVerificationCheckResponse | null>
  clearError: () => void
  updateStatus: (status: Partial<EmailVerificationStatus>) => void
}

/**
 * 邮箱验证状态管理Hook
 */
export function useEmailVerification(options: UseEmailVerificationOptions = {}): UseEmailVerificationReturn {
  const { email, checkInterval = 30000, autoCheck = false } = options

  // 状态管理
  const [verificationStatus, setVerificationStatus] = useState<EmailVerificationStatus>({
    required: false,
    sent: false,
    canResend: false,
  })
  
  const [isResending, setIsResending] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [cooldownSeconds, setCooldownSeconds] = useState(0)

  // 冷却时间倒计时
  useEffect(() => {
    if (cooldownSeconds > 0) {
      const timer = setTimeout(() => setCooldownSeconds(cooldownSeconds - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [cooldownSeconds])

  // 自动检查验证状态
  useEffect(() => {
    if (!autoCheck || !email) return

    const checkStatus = async () => {
      try {
        setIsChecking(true)
        const response = await AuthService.checkEmailVerification(email)
        
        if (response.emailVerified) {
          // 邮箱已验证，可以停止检查
          setVerificationStatus(prev => ({ ...prev, required: false }))
          return true // 返回true表示已验证
        }
      } catch (error) {
        console.error('检查验证状态失败:', error)
      } finally {
        setIsChecking(false)
      }
      return false
    }

    // 立即检查一次
    checkStatus().then(verified => {
      if (verified) return // 如果已验证，不需要设置定时器
      
      // 设置定时检查
      const interval = setInterval(async () => {
        const verified = await checkStatus()
        if (verified) {
          clearInterval(interval)
        }
      }, checkInterval)

      return () => clearInterval(interval)
    })
  }, [email, autoCheck, checkInterval])

  // 重发验证邮件
  const resendEmail = useCallback(async () => {
    if (!email || cooldownSeconds > 0 || isResending) return

    try {
      setIsResending(true)
      setError(null)
      
      const response = await AuthService.resendVerificationEmail(email)
      
      if (response.success) {
        setVerificationStatus(prev => ({
          ...prev,
          sent: true,
          lastSentAt: new Date(),
          canResend: response.canResendAgain
        }))
        
        // 设置冷却时间
        if (response.cooldownSeconds) {
          setCooldownSeconds(response.cooldownSeconds)
        } else {
          setCooldownSeconds(120) // 默认2分钟冷却
        }
      }
    } catch (error) {
      const errorMessage = handleAuthError(error)
      setError(errorMessage)
    } finally {
      setIsResending(false)
    }
  }, [email, cooldownSeconds, isResending])

  // 手动检查验证状态
  const checkVerificationStatus = useCallback(async (): Promise<EmailVerificationCheckResponse | null> => {
    if (!email) return null

    try {
      setIsChecking(true)
      setError(null)
      
      const response = await AuthService.checkEmailVerification(email)
      return response
    } catch (error) {
      const errorMessage = handleAuthError(error)
      setError(errorMessage)
      return null
    } finally {
      setIsChecking(false)
    }
  }, [email])

  // 清除错误
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // 更新验证状态
  const updateStatus = useCallback((status: Partial<EmailVerificationStatus>) => {
    setVerificationStatus(prev => ({ ...prev, ...status }))
  }, [])

  return {
    verificationStatus,
    isResending,
    isChecking,
    error,
    cooldownSeconds,
    resendEmail,
    checkVerificationStatus,
    clearError,
    updateStatus
  }
}
