<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base AI Input Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 40px;
        }
        .test-section {
            margin-bottom: 60px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #555;
        }
        .feature-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .feature-list h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 5px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Base AI Input Component Test</h1>
        
        <div class="feature-list">
            <h3>融合的UI特性：</h3>
            <ul>
                <li>✅ 原始英文占位符文本</li>
                <li>✅ 完美的单行输入布局：items-center 对齐</li>
                <li>✅ 固定的扩展高度：60px 扩展控件区域</li>
                <li>✅ 精确的按钮样式和间距</li>
                <li>✅ 完整的Think/Deep Search扩展控件样式</li>
                <li>✅ 简化的高度计算逻辑</li>
                <li>✅ 保留完整的features配置系统</li>
                <li>✅ 支持多行模式</li>
                <li>✅ 受控/非受控模式支持</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">1. 单行模式 - 完整功能</div>
            <div id="test1"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 多行模式 - 基础功能</div>
            <div id="test2"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 最小功能配置</div>
            <div id="test3"></div>
        </div>
    </div>

    <script>
        console.log('Base AI Input Component 已成功融合原始UI设计！');
        console.log('主要改进点：');
        console.log('- 恢复原始英文占位符');
        console.log('- 简化高度计算逻辑');
        console.log('- 修复单行模式布局对齐');
        console.log('- 统一按钮样式和tooltips');
        console.log('- 保持完整的功能配置系统');
    </script>
</body>
</html>