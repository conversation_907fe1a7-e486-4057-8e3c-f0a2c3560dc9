/**
 * 邮箱验证等待页面 - 客户端组件
 * 处理交互逻辑、状态管理和API调用
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { RefreshCw, CheckCircle, ExternalLink } from 'lucide-react'
import { useEmailVerification } from '@/hooks/use-email-verification'
import { cn } from '@/lib/utils'

export function EmailVerificationPendingClient() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const t = useTranslations('auth.verification')
  
  // 从URL参数获取邮箱地址
  const email = searchParams.get('email') || ''
  
  // 如果没有邮箱参数，重定向到注册页面
  useEffect(() => {
    if (!email) {
      router.push('/register')
    }
  }, [email, router])

  // 使用邮箱验证Hook
  const {
    verificationStatus,
    isResending,
    isChecking,
    error,
    cooldownSeconds,
    resendEmail,
    checkVerificationStatus,
    clearError
  } = useEmailVerification({
    email,
    autoCheck: true,
    checkInterval: 30000 // 30秒检查一次
  })

  // 监听验证状态变化
  useEffect(() => {
    const checkStatus = async () => {
      const response = await checkVerificationStatus()
      if (response?.emailVerified) {
        // 验证成功，跳转到成功页面
        router.push('/verify-success')
      }
    }

    // 每30秒检查一次
    const interval = setInterval(checkStatus, 30000)
    return () => clearInterval(interval)
  }, [checkVerificationStatus, router])

  const handleResendEmail = async () => {
    clearError()
    await resendEmail()
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  if (!email) {
    return null // 等待重定向
  }

  return (
    <div className="space-y-6">
      {/* 邮箱地址显示 */}
      <div className="text-center">
        <p className="text-gray-600">
          验证邮件已发送至
        </p>
        <p className="font-semibold text-gray-900 break-all">
          {email}
        </p>
      </div>

      {/* 操作指引 */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="font-semibold text-blue-900 mb-2">接下来怎么做？</h3>
        <ol className="text-sm text-blue-800 space-y-1">
          <li className="flex items-start">
            <span className="font-medium mr-2">1.</span>
            <span>查收您的邮箱（包括垃圾邮件文件夹）</span>
          </li>
          <li className="flex items-start">
            <span className="font-medium mr-2">2.</span>
            <span>点击邮件中的"验证邮箱"按钮</span>
          </li>
          <li className="flex items-start">
            <span className="font-medium mr-2">3.</span>
            <span>完成验证后即可正常使用账户</span>
          </li>
        </ol>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">{error}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearError}
            className="mt-2 text-red-600 hover:text-red-700"
          >
            关闭
          </Button>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="space-y-3">
        <Button
          onClick={handleResendEmail}
          disabled={cooldownSeconds > 0 || isResending}
          className="w-full"
          variant="outline"
        >
          <RefreshCw className={cn(
            "w-4 h-4 mr-2",
            isResending && "animate-spin"
          )} />
          {cooldownSeconds > 0
            ? `重新发送邮件 (${cooldownSeconds}s)`
            : isResending
              ? '发送中...'
              : '重新发送邮件'
          }
        </Button>

        <Button
          onClick={handleBackToLogin}
          variant="ghost"
          className="w-full"
        >
          返回登录
        </Button>
      </div>

      {/* 状态指示器 */}
      {isChecking && (
        <div className="flex items-center justify-center text-sm text-gray-500">
          <RefreshCw className="w-3 h-3 animate-spin mr-1" />
          正在检查验证状态...
        </div>
      )}

      {/* 帮助信息 */}
      <div className="text-center text-xs text-gray-500 space-y-1">
        <p>没有收到邮件？请检查垃圾邮件文件夹</p>
        <p>
          如需帮助，请
          <Button
            variant="link"
            size="sm"
            className="text-xs p-0 h-auto text-blue-600 hover:text-blue-500"
            onClick={() => window.open('/support', '_blank')}
          >
            联系客服
            <ExternalLink className="w-3 h-3 ml-1" />
          </Button>
        </p>
      </div>
    </div>
  )
}
