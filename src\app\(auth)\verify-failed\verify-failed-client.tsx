/**
 * 邮箱验证失败页面 - 客户端组件
 * 处理失败情况和提供解决方案
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RefreshCw, ArrowLeft, ExternalLink, Mail } from 'lucide-react'
import { useEmailVerification } from '@/hooks/use-email-verification'
import { cn } from '@/lib/utils'

export function VerifyFailedClient() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const t = useTranslations('auth.verification')
  
  // 从URL参数获取错误信息
  const errorParam = searchParams.get('error') || ''
  
  const [email, setEmail] = useState('')
  const [showResendForm, setShowResendForm] = useState(false)

  // 使用邮箱验证Hook
  const {
    isResending,
    error,
    resendEmail,
    clearError
  } = useEmailVerification({ email })

  const handleResendEmail = async () => {
    if (!email.trim()) {
      return
    }
    
    clearError()
    try {
      await resendEmail()
      // 成功后跳转到等待页面
      router.push(`/email-verification-pending?email=${encodeURIComponent(email)}`)
    } catch (error) {
      // 错误已经在Hook中处理
    }
  }

  const handleBackToRegister = () => {
    router.push('/register')
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  const handleShowResendForm = () => {
    setShowResendForm(true)
  }

  const getErrorDetails = (errorParam: string) => {
    switch (errorParam) {
      case 'invalid_token':
        return {
          title: '验证链接格式错误',
          description: '验证链接可能被截断或损坏',
          reasons: [
            '邮件客户端截断了链接',
            '复制链接时不完整',
            '链接格式不正确'
          ]
        }
      case 'token_expired':
        return {
          title: '验证链接已过期',
          description: '验证链接的有效期为1小时',
          reasons: [
            '验证链接已过期（有效期1小时）',
            '邮件发送时间过久',
            '需要重新发送验证邮件'
          ]
        }
      case 'token_used':
        return {
          title: '验证链接已被使用',
          description: '该验证链接已经被使用过',
          reasons: [
            '验证链接已被使用',
            '账户可能已经验证成功',
            '请尝试直接登录'
          ]
        }
      default:
        return {
          title: '验证失败',
          description: errorParam || '验证过程中出现未知错误',
          reasons: [
            '验证链接已过期（有效期1小时）',
            '验证链接已被使用',
            '链接格式不正确',
            '网络连接问题'
          ]
        }
    }
  }

  const errorDetails = getErrorDetails(errorParam)

  return (
    <div className="space-y-6">
      {/* 错误详情 */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="font-semibold text-red-800 mb-2">{errorDetails.title}</h3>
        <p className="text-red-700 text-sm mb-3">{errorDetails.description}</p>
        
        <div className="text-sm text-red-700">
          <p className="font-medium mb-2">可能的原因：</p>
          <ul className="space-y-1">
            {errorDetails.reasons.map((reason, index) => (
              <li key={index} className="flex items-start">
                <span className="mr-2">•</span>
                <span>{reason}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* 解决方案 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-semibold text-blue-800 mb-2">解决方案</h3>
        <div className="text-sm text-blue-700 space-y-2">
          <p>• 重新发送验证邮件获取新的验证链接</p>
          <p>• 检查邮箱中是否有其他验证邮件</p>
          <p>• 尝试直接登录（如果账户已验证）</p>
          <p>• 使用其他邮箱重新注册</p>
        </div>
      </div>

      {/* 当前错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">{error}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearError}
            className="mt-2 text-red-600 hover:text-red-700"
          >
            关闭
          </Button>
        </div>
      )}

      {/* 重发邮件表单 */}
      {showResendForm ? (
        <div className="space-y-4">
          <div>
            <Label htmlFor="email">邮箱地址</Label>
            <Input
              id="email"
              type="email"
              placeholder="请输入您的邮箱地址"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1"
            />
          </div>
          
          <Button
            onClick={handleResendEmail}
            disabled={!email.trim() || isResending}
            className="w-full"
          >
            <RefreshCw className={cn(
              "w-4 h-4 mr-2",
              isResending && "animate-spin"
            )} />
            {isResending ? '发送中...' : '重新发送验证邮件'}
          </Button>
        </div>
      ) : (
        <div className="space-y-3">
          <Button
            onClick={handleShowResendForm}
            className="w-full"
          >
            <Mail className="w-4 h-4 mr-2" />
            重新发送验证邮件
          </Button>

          <div className="grid grid-cols-2 gap-3">
            <Button
              onClick={handleBackToLogin}
              variant="outline"
              className="w-full"
            >
              尝试登录
            </Button>

            <Button
              onClick={handleBackToRegister}
              variant="ghost"
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              重新注册
            </Button>
          </div>
        </div>
      )}

      {/* 帮助信息 */}
      <div className="text-center text-xs text-gray-500 space-y-1">
        <p>如果问题持续存在，请联系我们的技术支持</p>
        <p>
          <Button
            variant="link"
            size="sm"
            className="text-xs p-0 h-auto text-blue-600 hover:text-blue-500"
            onClick={() => window.open('/support', '_blank')}
          >
            联系客服
            <ExternalLink className="w-3 h-3 ml-1" />
          </Button>
        </p>
      </div>
    </div>
  )
}
