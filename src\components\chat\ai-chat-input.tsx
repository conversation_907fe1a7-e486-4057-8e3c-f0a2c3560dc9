"use client"

import { AIChatInput as UIBaseChatInput } from "@/components/ui/base-ai-input";

interface AIChatInputProps {
  onSendMessage?: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  showAttachButton?: boolean;
  showVoiceButton?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  maxRows?: number;
  minRows?: number;
  
  // 新增功能支持
  loading?: boolean;
  enableFileUpload?: boolean;
  onAttachClick?: () => void;
  className?: string;
  autoFocus?: boolean;
}

const AIChatInput: React.FC<AIChatInputProps> = ({
  onSendMessage = () => {},
  disabled = false,
  placeholder = '',
  showAttachButton = true,
  showVoiceButton = true,
  value = '',
  onChange = () => {},
  maxRows = 5,
  minRows = 1,
  // 新增功能
  loading = false,
  enableFileUpload = false,
  onAttachClick = () => {},
  className = '',
  autoFocus = false,
}) => {
  return (
    <UIBaseChatInput
      onSendMessage={onSendMessage}
      disabled={disabled || loading}  // loading时也应该禁用
      placeholder={placeholder}
      showAttachButton={enableFileUpload || showAttachButton}  // 支持enableFileUpload配置
      showVoiceButton={showVoiceButton}
      showThinkButton={false}      // 聊天场景不显示Think按钮
      showDeepSearchButton={false}  // 聊天场景不显示Deep Search按钮
      value={value}
      onChange={onChange}
      maxRows={maxRows}
      minRows={minRows}
      multiline={true}
      loading={loading}  // 传递loading状态
      containerClassName={className}  // 传递自定义样式
      autoFocus={autoFocus}  // 传递autoFocus
    />
  );
};

export { AIChatInput };
export type { AIChatInputProps };

