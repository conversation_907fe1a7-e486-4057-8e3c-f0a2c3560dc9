/**
 * 认证服务 - 处理邮箱验证注册流程的API调用
 * 使用现有的post函数保持一致性
 */

import { post, type ApiResponse } from '@/lib/request'
import type {
  RegisterRequest,
  RegisterResponse,
  EmailVerificationResponse,
  ResendVerificationResponse,
  EmailVerificationCheckResponse,
  InviteCodeValidationResponse,
  ApiError
} from '@/types/auth'

/**
 * 认证服务类
 * 提供邮箱验证注册流程相关的API调用方法
 */
export class AuthService {
  /**
   * 用户注册（支持邮箱验证）
   */
  static async register(data: RegisterRequest): Promise<RegisterResponse> {
    try {
      const response = await post<ApiResponse<RegisterResponse>>('/auth/sign-up-invite', data)
      
      if (response.success && response.data) {
        return response.data
      }
      
      throw new Error(response.message || '注册失败')
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  /**
   * 验证邮箱（处理邮件中的验证链接）
   */
  static async verifyEmail(token: string): Promise<EmailVerificationResponse> {
    try {
      const response = await post<ApiResponse<EmailVerificationResponse>>('/auth/verify-email', { token })
      
      if (response.success && response.data) {
        return response.data
      }
      
      throw new Error(response.message || '邮箱验证失败')
    } catch (error) {
      console.error('邮箱验证失败:', error)
      throw error
    }
  }

  /**
   * 重发验证邮件
   */
  static async resendVerificationEmail(email: string): Promise<ResendVerificationResponse> {
    try {
      const response = await post<ApiResponse<ResendVerificationResponse>>('/auth/resend-verification', { email })
      
      if (response.success && response.data) {
        return response.data
      }
      
      throw new Error(response.message || '重发邮件失败')
    } catch (error) {
      console.error('重发验证邮件失败:', error)
      throw error
    }
  }

  /**
   * 检查邮箱验证状态
   */
  static async checkEmailVerification(email: string): Promise<EmailVerificationCheckResponse> {
    try {
      const response = await post<ApiResponse<EmailVerificationCheckResponse>>('/auth/check-email-verification', { email })
      
      if (response.success && response.data) {
        return response.data
      }
      
      throw new Error(response.message || '检查验证状态失败')
    } catch (error) {
      console.error('检查邮箱验证状态失败:', error)
      throw error
    }
  }

  /**
   * 验证邀请码
   */
  static async validateInviteCode(inviteCode: string): Promise<InviteCodeValidationResponse> {
    try {
      const response = await post<ApiResponse<InviteCodeValidationResponse>>('/auth/validate-invite-code', { 
        invite_code: inviteCode 
      })
      
      if (response.success && response.data) {
        return response.data
      }
      
      throw new Error(response.message || '邀请码验证失败')
    } catch (error) {
      console.error('邀请码验证失败:', error)
      throw error
    }
  }
}

/**
 * 错误处理工具函数
 */
export function handleAuthError(error: unknown): string {
  if (error instanceof Error) {
    // 网络错误
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return '网络连接失败，请检查网络后重试'
    }

    // API错误
    try {
      const apiError: ApiError = JSON.parse(error.message)
      switch (apiError.error) {
        case 'VALIDATION_ERROR':
          return '输入信息有误，请检查后重试'
        case 'INVALID_INVITE_CODE':
          return '邀请码无效或已使用，请检查邀请码'
        case 'USER_EXISTS':
          return '该邮箱已注册，请直接登录或使用其他邮箱'
        case 'EMAIL_SEND_FAILED':
          return '验证邮件发送失败，请稍后重试'
        case 'EMAIL_VERIFICATION_FAILED':
          return '邮箱验证失败，请检查验证链接'
        case 'TOKEN_EXPIRED':
          return '验证链接已过期，请重新发送验证邮件'
        case 'TOKEN_INVALID':
          return '验证链接无效，请重新发送验证邮件'
        default:
          return apiError.message || '操作失败，请重试'
      }
    } catch {
      return error.message
    }
  }
  
  return '未知错误，请重试'
}
