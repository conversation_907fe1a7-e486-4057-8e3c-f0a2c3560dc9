/**
 * 邮箱验证处理页面
 * 处理邮件中的验证链接，调用验证API并跳转到相应结果页面
 */

import { Suspense } from 'react'
import { VerifyEmailClient } from './verify-email-client'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

// 服务端组件 - 处理页面结构和SEO
export default function VerifyEmailPage() {
  return (
    <div className="min-h-svh flex flex-col items-center justify-center p-4 sm:p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
            </div>
          </CardHeader>
          
          <CardContent>
            <Suspense fallback={<VerifyEmailFallback />}>
              <VerifyEmailClient />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// 加载状态组件
function VerifyEmailFallback() {
  return (
    <div className="text-center space-y-4">
      <div className="space-y-2">
        <div className="h-6 bg-gray-200 rounded animate-pulse mx-auto w-48"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse mx-auto w-64"></div>
      </div>
      <div className="h-2 bg-gray-200 rounded animate-pulse"></div>
    </div>
  )
}

// 页面元数据
export const metadata = {
  title: '验证邮箱 - SpecificAI',
  description: '正在验证您的邮箱地址，请稍候...',
}
