'use client'

import { RegisterForm } from '@/components/common/register-form'
import { useSession } from '@/components/providers/session-provider'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { User, UserPlus } from 'lucide-react'

export default function RegisterPage() {
  const { user, loading, isAuthenticated } = useSession()
  const router = useRouter()

  return (
    <div className="min-h-svh flex flex-col items-center p-4 sm:p-6 md:p-10 overflow-y-auto">
      <div className="flex w-full max-w-sm flex-col gap-4 sm:gap-6 my-auto py-4">
        <RegisterForm />
      </div>
    </div>
  )
}
