/**
 * 消息输入组件 - 重构版本
 * 简化的消息输入界面，支持基本的文本输入和发送功能
 */

'use client'

import React, { useState, useCallback, KeyboardEvent } from 'react'
import { Send, Paperclip, Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

// UI components
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface MessageInputProps {
  /** 输入值和处理 */
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSend: (message: string) => Promise<void>

  /** UI配置 */
  placeholder?: string
  disabled?: boolean
  loading?: boolean
  className?: string
  autoFocus?: boolean

  /** 功能开关 */
  enableFileUpload?: boolean

  /** 事件回调 */
  onAttachClick?: () => void
}

// ============================================================================
// 消息输入组件
// ============================================================================

export const MessageInput = React.forwardRef<HTMLTextAreaElement, MessageInputProps>(({
  value,
  onChange,
  onSend,
  placeholder = '输入消息...',
  disabled = false,
  loading = false,
  className,
  autoFocus = false,
  enableFileUpload = false,
  onAttachClick,
}, ref) => {
  const [isSending, setIsSending] = useState(false)

  // 处理发送消息
  const handleSend = useCallback(async () => {
    if (!value.trim() || disabled || loading || isSending) {
      return
    }

    setIsSending(true)
    try {
      await onSend(value.trim())
    } catch (err) {
      // Handle send error silently
    } finally {
      setIsSending(false)
    }
  }, [value, disabled, loading, isSending, onSend])

  // 处理键盘事件
  const handleKeyDown = useCallback((e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }, [handleSend])

  const isDisabled = disabled || loading || isSending
  const canSend = value.trim().length > 0 && !isDisabled

  return (
    <div className={cn('flex items-end gap-2', className)}>
      {/* 文件上传按钮 */}
      {enableFileUpload && (
        <Button
          variant="outline"
          size="icon"
          disabled={isDisabled}
          onClick={onAttachClick}
          className="flex-shrink-0"
        >
          <Paperclip className="h-4 w-4" />
        </Button>
      )}

      {/* 文本输入区域 */}
      <div className="relative flex-1">
        <Textarea
          ref={ref}
          value={value}
          onChange={onChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={isDisabled}
          autoFocus={autoFocus}
          className={cn(
            'min-h-[2.5rem] max-h-32 resize-none pr-12',
            'focus-visible:ring-1'
          )}
          rows={1}
        />
        
        {/* 发送按钮 */}
        <Button
          size="sm"
          disabled={!canSend}
          onClick={handleSend}
          className="absolute bottom-1 right-1 h-8 w-8 p-0"
        >
          {isSending ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  )
})

MessageInput.displayName = 'MessageInput'

export default MessageInput