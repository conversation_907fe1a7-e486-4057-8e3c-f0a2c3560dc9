/**
 * 认证相关的TypeScript类型定义
 * 支持邮箱验证注册流程
 */

// 注册请求数据类型
export interface RegisterRequest {
  email: string
  password: string
  name: string
  company_name: string
  invite_code: string
  language?: string
}

// 用户信息类型
export interface User {
  id: string
  email: string
  name: string
  company_name: string
  emailVerified: boolean
  language?: string
  createdAt?: string
  updatedAt?: string
}

// 邮箱验证状态类型
export interface EmailVerificationStatus {
  required: boolean
  sent: boolean
  canResend: boolean
  lastSentAt?: Date
  cooldownSeconds?: number
  error?: string
}

// 注册响应类型
export interface RegisterResponse {
  success: boolean
  message: string
  user: User
  nextSteps: {
    requireEmailVerification: boolean
    verificationEmailSent: boolean
    instructions: string
    canResendEmail: boolean
    resendEndpoint: string
    emailError?: string
    troubleshooting?: string
  }
}

// API错误类型
export interface ApiError {
  error: string
  message: string
  details?: any
}

// 错误类型枚举
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INVITE_CODE = 'INVALID_INVITE_CODE',
  USER_EXISTS = 'USER_EXISTS',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EMAIL_SEND_FAILED = 'EMAIL_SEND_FAILED',
  EMAIL_VERIFICATION_FAILED = 'EMAIL_VERIFICATION_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID'
}

// 注册流程步骤类型
export type RegistrationStep = 'form' | 'pending' | 'failed' | 'success'

// 邮箱验证响应类型
export interface EmailVerificationResponse {
  success: boolean
  message: string
  verified: boolean
  user?: User
}

// 重发验证邮件响应类型
export interface ResendVerificationResponse {
  success: boolean
  message: string
  canResendAgain: boolean
  cooldownSeconds?: number
}

// 邀请码验证响应类型
export interface InviteCodeValidationResponse {
  valid: boolean
  message: string
  details?: {
    code: string
    used: boolean
    expired: boolean
  }
}

// 验证邮件检查响应类型
export interface EmailVerificationCheckResponse {
  emailVerified: boolean
  user?: User
  message?: string
}
