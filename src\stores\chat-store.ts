/**
 * 统一聊天状态Store - 基于Socket.IO与后端bd_socket.py集成
 * 设计原则：单一store，完整功能，支持扩展
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'
import { io, Socket } from 'socket.io-client'
import type {
  BaseWebSocketMessage,
  MessagePayload,
  StreamingPayload,
  CheckpointPayload,
  ReportPayload,
  ErrorPayload,
} from '@/types/websocket-event-type'
import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

// ============================================================================
// 核心数据接口
// ============================================================================

export interface ChatSession {
  sessionId: string
  groupChatId: string
  userId: string
  organizationId: string
  messages: BaseWebSocketMessage[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface SocketConnectionState {
  status: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'
  socket: Socket | null
  error: string | null
  reconnectAttempts: number
  maxReconnectAttempts: number
}

export interface ChatState {
  // 会话管理
  sessions: Record<string, ChatSession>
  currentSessionId: string | null
  groupToSessionMap: Record<string, string>
  
  // Socket连接状态
  connection: SocketConnectionState
  
  // UI状态
  isLoading: boolean
  isSending: boolean
  error: string | null
  
  // 消息输入状态
  inputText: string
  isInputDisabled: boolean
}

export interface ChatActions {
  // 连接管理
  connect: (auth: { userId: string; groupChatId: string; organizationId: string }) => Promise<void>
  disconnect: () => void
  
  // 会话管理
  createSession: (groupChatId: string, userId: string, organizationId: string) => string
  ensureSessionForGroup: (groupChatId: string, userId: string, organizationId: string) => ChatSession
  setCurrentSession: (sessionId: string) => void
  
  // 消息操作
  sendMessage: (message: string) => Promise<void>
  sendInteraction: (step: string, selection: string) => Promise<void>
  
  // 消息处理
  addMessage: (message: BaseWebSocketMessage) => void
  updateMessage: (messageId: string, updates: Partial<BaseWebSocketMessage>) => void
  
  // UI状态
  setInputText: (text: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // 重置
  reset: () => void
}

// ============================================================================
// Store实现
// ============================================================================

const initialState: ChatState = {
  sessions: {},
  currentSessionId: null,
  groupToSessionMap: {},
  connection: {
    status: 'disconnected',
    socket: null,
    error: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
  },
  isLoading: false,
  isSending: false,
  error: null,
  inputText: '',
  isInputDisabled: false,
}

export const useChatStore = create<ChatState & ChatActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // ========================================================================
      // 连接管理
      // ========================================================================
      
      connect: async (auth) => {
        console.log('🚀 chat-store connect called with:', auth)
        const { connection } = get()
        
        // 如果已经连接，先断开
        if (connection.socket) {
          console.log('🔌 Disconnecting existing socket')
          connection.socket.disconnect()
        }

        set((state) => {
          state.connection.status = 'connecting'
          state.connection.error = null
        })
        console.log('🔄 Connection status set to connecting')

        try {
          const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:8000'
          console.log('🌍 Connecting to socket URL:', socketUrl)
          
          const socket = io(socketUrl, {
            auth: {
              user_id: auth.userId,
              group_chat_id: auth.groupChatId,
              organization_id: auth.organizationId,
            },
            transports: ['websocket', 'polling'],
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000,
            timeout: 20000,
            forceNew: true, // 强制创建新连接
          })
          console.log('🔌 Socket.IO instance created')

          // 设置事件监听器
          setupSocketListeners(socket, set, get)
          console.log('🎧 Socket listeners setup complete')

          set((state) => {
            // 使用类型断言避免immer类型错误
            ;(state.connection as any).socket = socket
            state.connection.status = 'connecting'
          })
          console.log('📦 Socket stored in state')

        } catch (error) {
          console.error('❌ Socket connection error:', error)
          set((state) => {
            state.connection.status = 'error'
            state.connection.error = error instanceof Error ? error.message : 'Connection failed'
          })
        }
      },

      disconnect: () => {
        const { connection } = get()
        
        if (connection.socket) {
          connection.socket.disconnect()
        }

        set((state) => {
          state.connection.socket = null
          state.connection.status = 'disconnected'
          state.connection.error = null
          state.connection.reconnectAttempts = 0
        })
      },

      // ========================================================================
      // 会话管理
      // ========================================================================

      createSession: (groupChatId, userId, organizationId) => {
        const sessionId = `session_${groupChatId}_${Date.now()}`
        const now = new Date().toISOString()

        set((state) => {
          state.sessions[sessionId] = {
            sessionId,
            groupChatId,
            userId,
            organizationId,
            messages: [],
            isActive: true,
            createdAt: now,
            updatedAt: now,
          }
          state.groupToSessionMap[groupChatId] = sessionId
          state.currentSessionId = sessionId
        })

        return sessionId
      },

      ensureSessionForGroup: (groupChatId, userId, organizationId) => {
        const existingSessionId = get().groupToSessionMap[groupChatId]
        if (existingSessionId && get().sessions[existingSessionId]) {
          return get().sessions[existingSessionId]
        }
        
        const sessionId = get().createSession(groupChatId, userId, organizationId)
        return get().sessions[sessionId]
      },

      setCurrentSession: (sessionId) => {
        set((state) => {
          state.currentSessionId = sessionId
        })
      },

      // ========================================================================
      // 消息操作
      // ========================================================================

      sendMessage: async (message) => {
        const { connection, currentSessionId, sessions } = get()
        
        if (!connection.socket || connection.status !== 'connected') {
          throw new Error('Socket not connected')
        }
        
        if (!currentSessionId || !sessions[currentSessionId]) {
          throw new Error('No active session')
        }

        const session = sessions[currentSessionId]

        set((state) => {
          state.isSending = true
          state.error = null
        })

        try {
          // 发送用户消息事件到后端
          connection.socket.emit('user_message', {
            message,
            session_id: session.sessionId,
            payload: {},
          })

          // 创建用户消息并添加到会话
          const userMessage: BaseWebSocketMessage = {
            id: `msg_${Date.now()}`,
            groupChatId: session.groupChatId,
            sessionId: session.sessionId,
            userId: session.userId,
            organizationId: session.organizationId,
            payload: {
              type: 'streaming',
              delta: message,
              isComplete: true,
            } as StreamingPayload,
            timestamp: new Date().toISOString(),
            status: 'sent',
          }

          get().addMessage(userMessage)

        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to send message'
          })
          throw error
        } finally {
          set((state) => {
            state.isSending = false
          })
        }
      },

      sendInteraction: async (step, selection) => {
        const { connection, currentSessionId, sessions } = get()
        
        if (!connection.socket || connection.status !== 'connected') {
          throw new Error('Socket not connected')
        }
        
        if (!currentSessionId || !sessions[currentSessionId]) {
          throw new Error('No active session')
        }

        const session = sessions[currentSessionId]

        try {
          connection.socket.emit('user_interaction', {
            step,
            selection,
            session_id: session.sessionId,
          })
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to send interaction'
          })
          throw error
        }
      },

      // ========================================================================
      // 消息处理
      // ========================================================================

      addMessage: (message) => {
        set((state) => {
          const session = state.sessions[message.sessionId]
          if (session) {
            session.messages.push(message)
            session.updatedAt = new Date().toISOString()
          }
        })
      },

      updateMessage: (messageId, updates) => {
        set((state) => {
          Object.values(state.sessions).forEach((session) => {
            const messageIndex = session.messages.findIndex(m => m.id === messageId)
            if (messageIndex !== -1) {
              Object.assign(session.messages[messageIndex], updates)
              session.updatedAt = new Date().toISOString()
            }
          })
        })
      },

      // ========================================================================
      // UI状态管理
      // ========================================================================

      setInputText: (text) => {
        set((state) => {
          state.inputText = text
        })
      },

      setLoading: (loading) => {
        set((state) => {
          state.isLoading = loading
        })
      },

      setError: (error) => {
        set((state) => {
          state.error = error
        })
      },

      // ========================================================================
      // 重置
      // ========================================================================

      reset: () => {
        set(() => initialState)
      },
    }))
  )
)

// ============================================================================
// Socket事件监听器设置
// ============================================================================

function setupSocketListeners(
  socket: Socket,
  set: any,
  get: () => ChatState & ChatActions
) {
  // 连接成功
  socket.on('connect', () => {
    console.log('Socket connected:', socket.id)
    set((state: ChatState) => {
      state.connection.status = 'connected'
      state.connection.error = null
      state.connection.reconnectAttempts = 0
    })
  })

  // 连接失败
  socket.on('connect_error', (error) => {
    console.error('❌ Socket connection error:', error)
    console.error('Error details:', {
      message: error.message,
      description: (error as any).description,
      context: (error as any).context,
      type: (error as any).type
    })
    set((state: ChatState) => {
      state.connection.status = 'error'
      state.connection.error = error.message || 'Connection failed'
    })
  })

  // 断开连接
  socket.on('disconnect', (reason) => {
    console.log('Socket disconnected:', reason)
    set((state: ChatState) => {
      state.connection.status = 'disconnected'
      state.connection.error = reason === 'io server disconnect' ? 'Server disconnected' : null
    })
  })

  // 重连中
  socket.on('reconnect_attempt', (attemptNumber) => {
    console.log('Socket reconnect attempt:', attemptNumber)
    set((state: ChatState) => {
      state.connection.status = 'reconnecting'
      state.connection.reconnectAttempts = attemptNumber
    })
  })

  // 处理状态更新
  socket.on('processing_status', (data) => {
    console.log('Processing status:', data)
    // 可以在这里添加状态更新的UI反馈
  })

  // 处理分析结果
  socket.on('analysis_result', (data) => {
    console.log('Analysis result:', data)
    
    const message: BaseWebSocketMessage = {
      id: `msg_${Date.now()}`,
      groupChatId: data.group_chat_id || '',
      sessionId: data.session_id || get().currentSessionId || '',
      userId: '',
      organizationId: '',
      payload: {
        type: 'report',
        content: data.message || 'Analysis completed',
      } as ReportPayload,
      timestamp: new Date().toISOString(),
    }

    get().addMessage(message)
  })

  // 处理交互需求
  socket.on('interaction_required', (data) => {
    console.log('Interaction required:', data)
    
    const message: BaseWebSocketMessage = {
      id: `msg_${Date.now()}`,
      groupChatId: data.group_chat_id || '',
      sessionId: data.session_id || get().currentSessionId || '',
      userId: '',
      organizationId: '',
      payload: {
        type: 'checkpoint',
        fields: data.options?.map((option: any) => ({
          id: option.id,
          label: option.name,
          type: 'radio' as const,
          required: true,
          defaultValue: '',
          options: [],
        })) || [],
      } as CheckpointPayload,
      timestamp: new Date().toISOString(),
    }

    get().addMessage(message)
  })

  // 处理最终结果
  socket.on('final_result', (data) => {
    console.log('Final result:', data)
    
    const message: BaseWebSocketMessage = {
      id: `msg_${Date.now()}`,
      groupChatId: data.group_chat_id || '',
      sessionId: data.session_id || get().currentSessionId || '',
      userId: '',
      organizationId: '',
      payload: {
        type: 'report',
        content: data.data?.report || data.message || 'Task completed',
      } as ReportPayload,
      timestamp: new Date().toISOString(),
    }

    get().addMessage(message)
  })

  // 处理错误
  socket.on('error', (data) => {
    console.error('Socket error:', data)
    
    const message: BaseWebSocketMessage = {
      id: `msg_${Date.now()}`,
      groupChatId: data.group_chat_id || '',
      sessionId: data.session_id || get().currentSessionId || '',
      userId: '',
      organizationId: '',
      payload: {
        type: 'error',
        message: data.message || 'An error occurred',
      } as ErrorPayload,
      timestamp: new Date().toISOString(),
    }

    get().addMessage(message)
  })

  // 处理聊天响应 - 支持流式处理
  socket.on('chat_response', (data) => {
    console.log('Chat response:', data)
    
    const { currentSessionId } = get()
    if (!currentSessionId) return

    // 检查是否为流式数据
    const isStreaming = data.streaming === true
    const messageId = data.message_id || `msg_streaming_${Date.now()}`

    if (isStreaming) {
      // 流式消息处理
      const existingMessages = get().sessions[currentSessionId]?.messages || []
      const existingMessageIndex = existingMessages.findIndex(m => m.id === messageId)

      if (existingMessageIndex !== -1) {
        // 更新现有流式消息
        get().updateMessage(messageId, {
          payload: {
            type: 'streaming',
            delta: (existingMessages[existingMessageIndex].payload as StreamingPayload).delta + (data.delta || ''),
            isComplete: data.isComplete || false,
          } as StreamingPayload,
          timestamp: new Date().toISOString(),
        })
      } else {
        // 创建新的流式消息
        const streamingMessage: BaseWebSocketMessage = {
          id: messageId,
          groupChatId: data.group_chat_id || '',
          sessionId: currentSessionId,
          userId: '',
          organizationId: '',
          payload: {
            type: 'streaming',
            delta: data.delta || data.message || '',
            isComplete: data.isComplete || false,
          } as StreamingPayload,
          timestamp: new Date().toISOString(),
        }
        get().addMessage(streamingMessage)
      }
    } else {
      // 非流式消息（完整消息）
      const message: BaseWebSocketMessage = {
        id: messageId,
        groupChatId: data.group_chat_id || '',
        sessionId: currentSessionId,
        userId: '',
        organizationId: '',
        payload: {
          type: 'streaming',
          delta: data.message || '',
          isComplete: true,
        } as StreamingPayload,
        timestamp: new Date().toISOString(),
      }
      get().addMessage(message)
    }
  })

  // 处理流式数据专用事件
  socket.on('streaming_data', (data) => {
    console.log('Streaming data:', data)
    
    const { currentSessionId } = get()
    if (!currentSessionId) return

    const messageId = data.message_id || `msg_streaming_${Date.now()}`
    const existingMessages = get().sessions[currentSessionId]?.messages || []
    const existingMessageIndex = existingMessages.findIndex(m => m.id === messageId)

    if (existingMessageIndex !== -1) {
      // 累积流式数据
      const existingPayload = existingMessages[existingMessageIndex].payload as StreamingPayload
      get().updateMessage(messageId, {
        payload: {
          type: 'streaming',
          delta: existingPayload.delta + (data.delta || ''),
          isComplete: data.isComplete || false,
        } as StreamingPayload,
        timestamp: new Date().toISOString(),
      })
    } else {
      // 创建新的流式消息
      const streamingMessage: BaseWebSocketMessage = {
        id: messageId,
        groupChatId: data.group_chat_id || '',
        sessionId: currentSessionId,
        userId: '',
        organizationId: '',
        payload: {
          type: 'streaming',
          delta: data.delta || '',
          isComplete: data.isComplete || false,
        } as StreamingPayload,
        timestamp: new Date().toISOString(),
      }
      get().addMessage(streamingMessage)
    }
  })
}

// ============================================================================
// Selector Hooks
// ============================================================================

export const useCurrentSession = () => {
  return useChatStore((state) => {
    if (!state.currentSessionId) return null
    return state.sessions[state.currentSessionId] || null
  })
}

export const useConnectionStatus = () => {
  return useChatStore((state) => state.connection.status)
}

export const useMessages = () => {
  return useChatStore((state) => {
    if (!state.currentSessionId) return []
    const session = state.sessions[state.currentSessionId]
    return session?.messages || []
  })
}

export const useIsLoading = () => {
  return useChatStore((state) => state.isLoading || state.isSending)
}

export const useChatConnection = () => {
  return useChatStore((state) => ({
    connect: state.connect,
    disconnect: state.disconnect,
    status: state.connection.status,
    error: state.connection.error,
  }))
}