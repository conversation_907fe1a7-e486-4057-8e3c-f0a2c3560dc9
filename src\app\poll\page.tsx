'use client'

import poll from '@/assets/poll.svg'
import { useSession } from '@/components/providers/session-provider'
import { Button } from '@/components/ui/button'
import { ArrowLeft, ArrowRight } from 'lucide-react'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useEffect, useMemo, useRef, useState } from 'react'
// 动态导入重型组件 - 延迟加载
import { useAsyncErrorHandler } from '@/hooks/useErrorHandler'
import { useHttp } from '@/hooks/useHttp'
import { cn } from '@/lib/utils'
import dynamic from 'next/dynamic'
const AIChatInput = dynamic(() => import('@/components/chat/ai-chat-input').then(mod => ({ default: mod.AIChatInput })), {
  loading: () => <div className="h-12 bg-gray-100 rounded-lg animate-pulse" />,
  ssr: false
})
// import { type ApiResponse } from '@/lib/request' // 暂时不使用

// 本地缓存键名
const POLL_CACHE_KEY = 'poll_data'

// API 响应类型定义
interface SurveyQuestion {
  title: string
  options: Record<string, string>
}

// 动态的问卷数据结构 - 不假定任何字段
type SurveyQuestionsData = Record<string, SurveyQuestion>

// 使用 request.ts 中的 ApiResponse 类型 - 注释掉暂时不使用的类型
// type SurveyQuestionsResponse = ApiResponse<SurveyQuestionsData>

// 问卷答案格式
interface SurveyAnswer {
  field: string
  options: string[]
  user_input: string | null
}

// 问卷提交数据格式
interface SurveySubmitData {
  answers: SurveyAnswer[]
}

interface SurveySubmitResponse {
  success: boolean
  message: string
  data?: any
}

// 新的问卷数据结构 - 完全动态
interface PollData {
  currentStepIndex: number // 当前步骤索引
  answers: SurveyAnswer[] // 每个步骤的答案
  stepFields: string[] // 步骤字段列表（从接口动态获取）
  timestamp: number
}

/**
 * 完全数据驱动的多步骤问卷页面
 * 根据接口返回的数据动态生成步骤，支持智能缓存恢复
 */
export default function PollPage() {
  const router = useRouter()
  const { user, organization } = useSession()
  const t = useTranslations('poll')
  const tCommon = useTranslations('common')
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // HTTP 客户端 - 启用自动错误处理
  const { get, post } = useHttp({
    showLoading: false,
    enableAutoErrorToast: true,
  })

  // 异步错误处理
  const { safeAsync } = useAsyncErrorHandler()

  // 问卷选项数据状态
  const [surveyQuestions, setSurveyQuestions] = useState<SurveyQuestionsData | null>(null)
  const [isLoadingQuestions, setIsLoadingQuestions] = useState(true)

  // 问卷状态 - 初始状态为空，等待接口数据后动态初始化
  const [pollData, setPollData] = useState<PollData | null>(null)

  // ============================================================================
  // 动态数据处理工具函数
  // ============================================================================

  // 从接口数据创建初始的问卷数据结构
  const createInitialPollData = (surveyData: SurveyQuestionsData): PollData => {
    const stepFields = Object.keys(surveyData)
    const answers = stepFields.map(field => ({
      field,
      options: [] as string[],
      user_input: null as string | null,
    }))

    return {
      currentStepIndex: 0,
      answers,
      stepFields,
      timestamp: Date.now(),
    }
  }

  // 获取当前步骤的字段名
  const getCurrentStepField = (): string => {
    if (!pollData || pollData.stepFields.length === 0) return ''
    return pollData.stepFields[pollData.currentStepIndex] || ''
  }

  // 获取步骤总数
  const getTotalSteps = (): number => {
    return pollData?.stepFields.length || 0
  }

  // isStepCompleted 函数的逻辑已内联到需要的地方

  // findNextIncompleteStep 函数的逻辑已内联到缓存恢复中

  // ============================================================================
  // 数据获取和初始化逻辑
  // ============================================================================

  // 智能缓存管理
  const cachedData = useMemo(() => {
    if (typeof window === 'undefined') return null
    
    try {
      const cached = localStorage.getItem(POLL_CACHE_KEY)
      if (!cached) return null
      
      const data = JSON.parse(cached)
      // 检查缓存版本和时效性（24小时）
      if (data.version === '1.0' && (Date.now() - data.lastUpdated) < 86400000) {
        return data
      }
    } catch (error) {
      console.warn('缓存恢复失败:', error)
    }
    return null
  }, [])

  // 防抖缓存写入
  const debouncedCacheWrite = useMemo(() => {
    let timeoutId: NodeJS.Timeout
    return (data: any) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        if (typeof window !== 'undefined') {
          try {
            localStorage.setItem(POLL_CACHE_KEY, JSON.stringify({
              ...data,
              version: '1.0',
              lastUpdated: Date.now()
            }))
          } catch (error) {
            console.warn('缓存写入失败:', error)
          }
        }
      }, 500)
    }
  }, [])

  // 异步非阻塞数据获取
  useEffect(() => {
    let isMounted = true
    
    // 首先尝试从缓存恢复
    if (cachedData?.surveyQuestions) {
      setSurveyQuestions(cachedData.surveyQuestions)
      if (cachedData.answers && cachedData.stepFields) {
        const pollDataFromCache = {
          surveyQuestions: cachedData.surveyQuestions,
          answers: cachedData.answers,
          stepFields: cachedData.stepFields,
          currentStepIndex: cachedData.currentStepIndex || 0,
          timestamp: cachedData.timestamp || Date.now()
        }
        setPollData(pollDataFromCache)
      }
      setIsLoadingQuestions(false)
    } else {
      setIsLoadingQuestions(true)
    }

    const fetchSurveyQuestions = async () => {

      const result = await safeAsync(
        async () => {
          const response = await get<SurveyQuestionsData>('/ovs_profile/survey_questions')
          return response
        },
        {
          customMessage: '获取问卷数据失败，请检查网络连接后重试',
          criticalError: false,
          onError: processedError => {
            console.error('❌ 获取问卷选项失败:', processedError)
          },
        }
      )

      if (result?.success && result.data && isMounted) {
        setSurveyQuestions(result.data)
        
        // 延迟初始化数据结构，优先页面渲染
        requestAnimationFrame(() => {
          const initialData = createInitialPollData(result.data!)
          setPollData(initialData)
          
          // 更新缓存
          if (!cachedData || JSON.stringify(result.data) !== JSON.stringify(cachedData.surveyQuestions)) {
            debouncedCacheWrite({
              surveyQuestions: result.data,
              answers: initialData.answers,
              stepFields: initialData.stepFields,
              currentStepIndex: initialData.currentStepIndex,
              timestamp: initialData.timestamp
            })
          }
        })
      }

      if (isMounted) {
        setIsLoadingQuestions(false)
      }
    }

    // 使用setTimeout延迟数据获取，优先页面渲染
    const timeoutId = setTimeout(fetchSurveyQuestions, 100)

    return () => {
      isMounted = false
      clearTimeout(timeoutId)
    }
  }, [cachedData, debouncedCacheWrite, get, safeAsync])

  // 获取当前步骤的选项列表
  const getCurrentStepOptions = (): string[] => {
    if (!surveyQuestions || !pollData) return []

    const currentField = getCurrentStepField()
    const currentQuestion = surveyQuestions[currentField]

    return currentQuestion ? Object.keys(currentQuestion.options) : []
  }

  // 获取当前步骤的标题
  const getCurrentStepTitle = (): string => {
    if (!surveyQuestions || !pollData) return ''

    const currentField = getCurrentStepField()
    const currentQuestion = surveyQuestions[currentField]

    return currentQuestion?.title || t(`steps.${currentField}.title`) || ''
  }

  // 获取当前步骤的选中答案
  const getCurrentStepAnswers = (): string[] => {
    if (!pollData) return []

    const currentAnswer = pollData.answers[pollData.currentStepIndex]
    return currentAnswer ? currentAnswer.options : []
  }

  // 获取当前步骤的用户输入
  const getCurrentStepUserInput = (): string => {
    if (!pollData) return ''

    const currentAnswer = pollData.answers[pollData.currentStepIndex]
    return currentAnswer?.user_input || ''
  }

  // ============================================================================
  // 智能缓存处理逻辑
  // ============================================================================

  // 验证缓存数据是否与当前接口数据兼容
  const isCacheCompatible = (cachedData: any, surveyData: SurveyQuestionsData): boolean => {
    if (!cachedData || !cachedData.stepFields || !cachedData.answers) return false

    const currentFields = Object.keys(surveyData)
    const cachedFields = cachedData.stepFields

    // 检查字段是否完全匹配
    if (currentFields.length !== cachedFields.length) return false

    return currentFields.every(field => cachedFields.includes(field))
  }

  // 从缓存恢复数据，并智能跳转到未完成的步骤
  const restoreFromCache = (surveyData: SurveyQuestionsData) => {
    try {
      const cachedData = localStorage.getItem(POLL_CACHE_KEY)
      if (!cachedData) return

      const cached = JSON.parse(cachedData)

      if (isCacheCompatible(cached, surveyData)) {
        // 缓存兼容，恢复数据
        const restoredData: PollData = {
          ...cached,
          timestamp: Date.now(), // 更新时间戳
        }

        // 智能跳转到第一个未完成的步骤（既没有选项也没有用户输入）
        let nextIncompleteIndex = 0
        let foundIncomplete = false

        for (let i = 0; i < restoredData.answers.length; i++) {
          const answer = restoredData.answers[i]
          const hasOptions = answer.options.length > 0
          const hasUserInput = !!(answer.user_input && answer.user_input.trim().length > 0)

          if (!hasOptions && !hasUserInput) {
            nextIncompleteIndex = i
            foundIncomplete = true
            break
          }
        }

        // 如果所有步骤都完成了，停留在最后一步
        if (!foundIncomplete) {
          nextIncompleteIndex = Math.max(0, restoredData.answers.length - 1)
        }

        restoredData.currentStepIndex = nextIncompleteIndex

        setPollData(restoredData)
        console.log('📋 从缓存恢复问卷数据:', {
          currentStep: nextIncompleteIndex + 1,
          totalSteps: restoredData.stepFields.length,
          completedSteps: restoredData.answers.filter(answer => {
            const hasOptions = answer.options.length > 0
            const hasUserInput = !!(answer.user_input && answer.user_input.trim().length > 0)
            return hasOptions || hasUserInput
          }).length,
          answers: restoredData.answers.map((answer, index) => {
            const hasOptions = answer.options.length > 0
            const hasUserInput = !!(answer.user_input && answer.user_input.trim().length > 0)
            return {
              step: index + 1,
              field: answer.field,
              optionsCount: answer.options.length,
              hasUserInput,
              isCompleted: hasOptions || hasUserInput
            }
          })
        })
        console.log(`🎯 智能跳转到步骤 ${nextIncompleteIndex + 1}/${restoredData.stepFields.length}`)
      } else {
        // 缓存不兼容，使用新的数据结构
        console.log('⚠️ 缓存数据与当前接口不兼容，使用新数据结构')
        const newData = createInitialPollData(surveyData)
        setPollData(newData)
      }
    } catch (error) {
      console.warn('⚠️ 缓存恢复失败:', error)
      const newData = createInitialPollData(surveyData)
      setPollData(newData)
    }
  }

  // 保存数据到本地缓存
  useEffect(() => {
    if (!pollData) return

    const saveCachedData = () => {
      try {
        localStorage.setItem(POLL_CACHE_KEY, JSON.stringify(pollData))
        console.log('💾 保存问卷数据到缓存:', {
          currentStep: pollData.currentStepIndex + 1,
          totalSteps: pollData.stepFields.length,
          answers: pollData.answers.map((answer, index) => {
            const hasOptions = answer.options.length > 0
            const hasUserInput = !!(answer.user_input && answer.user_input.trim().length > 0)
            return {
              step: index + 1,
              field: answer.field,
              optionsCount: answer.options.length,
              hasUserInput,
              isCompleted: hasOptions || hasUserInput
            }
          })
        })
      } catch (error) {
        console.warn('⚠️ 缓存保存失败:', error)
      }
    }

    // 防抖保存，避免频繁写入
    const timeoutId = setTimeout(saveCachedData, 300)
    return () => clearTimeout(timeoutId)
  }, [pollData])

  // 当接口数据加载完成后，尝试从缓存恢复
  useEffect(() => {
    if (surveyQuestions && !pollData) {
      restoreFromCache(surveyQuestions)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [surveyQuestions]) // pollData 和 restoreFromCache 在这里不应该作为依赖

  // ============================================================================
  // 页面路由和权限检查
  // ============================================================================

  // 如果用户未登录，重定向到登录页面
  useEffect(() => {
    if (!user) {
      router.push('/login')
    }
  }, [user, router])

  // 如果已经完成公司资料，重定向到主页面
  useEffect(() => {
    if (organization?.hasCompanyProfile === true) {
      router.push('/chat')
    }
  }, [organization?.hasCompanyProfile, router])

  // 自动聚焦到输入框
  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus()
      }
    }, 100)
    return () => clearTimeout(timer)
  }, [pollData?.currentStepIndex])

  // ============================================================================
  // 早期退出渲染
  // ============================================================================

  // 如果用户未登录，显示加载状态
  if (!user) {
    return null
  }

  // 如果已经完成公司资料，显示加载状态
  if (organization?.hasCompanyProfile === true) {
    return null
  }

  // 如果问卷选项还在加载中，显示加载状态
  if (isLoadingQuestions || !surveyQuestions || !pollData) {
    return (
      <div className="h-full overflow-hidden bg-gray-50 flex flex-col items-center justify-center p-6">
        <div className="w-full max-w-2xl p-8 flex flex-col items-center h-full justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">{t('loading')}</p>
        </div>
      </div>
    )
  }

  // ============================================================================
  // 用户交互处理函数
  // ============================================================================

  // 处理选项标签点击
  const handleTagClick = (tag: string) => {
    if (!pollData) return

    const currentAnswers = getCurrentStepAnswers()
    let newAnswers: string[]

    if (currentAnswers.includes(tag)) {
      newAnswers = currentAnswers.filter((t: string) => t !== tag)
    } else {
      newAnswers = [...currentAnswers, tag]
    }

    setPollData(prev => {
      if (!prev) return prev

      const newAnswers_copy = [...prev.answers]
      newAnswers_copy[prev.currentStepIndex] = {
        ...newAnswers_copy[prev.currentStepIndex],
        options: newAnswers,
      }

      return {
        ...prev,
        answers: newAnswers_copy,
        timestamp: Date.now(),
      }
    })
  }

  // 处理用户输入变化 - 适配AIChatInput接口
  const handleUserInputChange = (value: string) => {
    if (!pollData) return

    setPollData(prev => {
      if (!prev) return prev

      const newAnswers = [...prev.answers]
      newAnswers[prev.currentStepIndex] = {
        ...newAnswers[prev.currentStepIndex],
        user_input: value,
      }

      return {
        ...prev,
        answers: newAnswers,
        timestamp: Date.now(),
      }
    })
  }

  // 下一步
  const handleNext = () => {
    if (!pollData) return

    const nextStepIndex = pollData.currentStepIndex + 1
    if (nextStepIndex < getTotalSteps()) {
      setPollData(prev =>
        prev
          ? {
              ...prev,
              currentStepIndex: nextStepIndex,
              timestamp: Date.now(),
            }
          : prev
      )
    }
  }

  // 上一步
  const handlePrevious = () => {
    if (!pollData) return

    if (pollData.currentStepIndex > 0) {
      setPollData(prev =>
        prev
          ? {
              ...prev,
              currentStepIndex: prev.currentStepIndex - 1,
              timestamp: Date.now(),
            }
          : prev
      )
    } else {
      router.back()
    }
  }

  // 提交问卷
  const handleSubmit = async () => {
    if (!pollData) return

    // 准备提交数据 - 直接提交答案数组，不包含 company_info
    const submitData: SurveySubmitData = {
      answers: pollData.answers,
    }

    console.log('📤 提交问卷数据:', submitData)

    const result = await safeAsync(
      async () => {
        const response = await post<SurveySubmitResponse>('/ovs_profile/submit_survey', submitData)
        return response
      },
      {
        customMessage: '问卷提交失败，请重试',
        criticalError: false,
        onError: processedError => {
          console.error('❌ 问卷提交失败:', processedError)
        },
      }
    )

    if (result?.success) {
      console.log('✅ 问卷提交成功:', result.data)

      // 提交成功后清除缓存
      localStorage.removeItem(POLL_CACHE_KEY)

      // 跳转到聊天页面
      router.push('/chat')
    }
  }

  // 检查当前步骤是否可以继续
  const canProceed = (): boolean => {
    if (!pollData) return false

    // 检查当前步骤是否至少选择了一个选项或者输入了文本
    const currentAnswer = pollData.answers[pollData.currentStepIndex]
    if (!currentAnswer) return false

    const hasOptions = currentAnswer.options.length > 0
    const hasUserInput = !!(currentAnswer.user_input && currentAnswer.user_input.trim().length > 0)

    return hasOptions || hasUserInput
  }

  // 检查是否是最后一步
  const isLastStep = (): boolean => {
    if (!pollData) return false
    return pollData.currentStepIndex === getTotalSteps() - 1
  }

  // ============================================================================
  // 渲染函数
  // ============================================================================

  // 渲染动态步骤指示器
  const renderStepIndicator = () => {
    if (!pollData) return null

    const totalSteps = getTotalSteps()

    return (
      <div className="flex items-center justify-center mb-6">
        <div className="flex items-center space-x-2">
          {Array.from({ length: totalSteps }, (_, index) => (
            <div key={index} className="flex items-center">
              <div
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  index <= pollData.currentStepIndex
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-500'
                )}
              >
                {index + 1}
              </div>
              {index < totalSteps - 1 && (
                <div
                  className={cn(
                    'w-8 h-0.5 mx-2',
                    index < pollData.currentStepIndex ? 'bg-blue-500' : 'bg-gray-200'
                  )}
                />
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }

  // 渲染标签选择
  const renderTagSelection = () => {
    if (!pollData || !surveyQuestions) return null

    const options = getCurrentStepOptions()
    const currentAnswers = getCurrentStepAnswers()
    const currentField = getCurrentStepField()

    // 获取选项文本，优先使用 API 数据，后备使用国际化
    const getOptionText = (optionKey: string) => {
      if (surveyQuestions && surveyQuestions[currentField]) {
        return surveyQuestions[currentField].options[optionKey] || optionKey
      }

      // 后备：使用国际化
      return t(`steps.${currentField}.options.${optionKey}`) || optionKey
    }

    return (
      <div className="w-full mb-6">
        <div className="flex flex-wrap gap-3 justify-center mb-4">
          {options.map(optionKey => {
            const optionText = getOptionText(optionKey)
            return (
              <button
                key={optionKey}
                onClick={() => handleTagClick(optionKey)}
                className={cn(
                  'px-4 py-2 rounded-full text-sm font-medium transition-all cursor-pointer',
                  currentAnswers.includes(optionKey)
                    ? 'bg-blue-100 text-blue-700 border-2 border-blue-300'
                    : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
                )}
              >
                {optionText}
              </button>
            )
          })}
        </div>
      </div>
    )
  }

  // 渲染用户输入框 - 每个步骤都有
  const renderUserInput = () => {
    if (!pollData) return null

    const currentField = getCurrentStepField()
    const currentUserInput = getCurrentStepUserInput()

    const placeholder = t(`steps.${currentField}.userInputPlaceholder`) ||
                       t('common.userInputPlaceholder') ||
                       '请输入补充信息，或选择上方选项...'

    return (
      <div className="w-full mb-8">
        <div className="mb-2 text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
          💡 您可以选择上方的选项，或者在下方输入文本，或者两者都选择
        </div>
        <AIChatInput
          value={currentUserInput}
          onChange={handleUserInputChange}
          onSendMessage={async (message) => {
            if (isLastStep()) {
              await handleSubmit()
            }
          }}
          placeholder={placeholder}
          className="w-full"
          autoFocus={true}
          // Poll场景配置：不显示任何动作按钮
          showAttachButton={false}
          showVoiceButton={false}
        />
      </div>
    )
  }

  // ============================================================================
  // 主要渲染
  // ============================================================================

  return (
    <div className="h-full overflow-hidden bg-gray-50 flex flex-col items-center justify-center p-6">
      <div className="w-full max-w-2xl p-8 flex flex-col items-center h-full overflow-auto">
        {/* 标题 */}
        <h1 className="text-2xl font-semibold text-gray-900 mb-3">{t('title')}</h1>

        {/* 卡通图标 */}
        <div className="mb-6">
          <Image src={poll} alt="poll" width={250} height={245} />
        </div>

        {/* 动态步骤指示器 */}
        {renderStepIndicator()}

        {/* 当前步骤标题 */}
        <h2 className="text-lg font-medium text-gray-700 mb-2">{getCurrentStepTitle()}</h2>

        {/* 动态步骤进度 */}
        <p className="text-sm text-gray-500 mb-8 text-center">
          {t('step', {
            step: (pollData?.currentStepIndex || 0) + 1,
            total: getTotalSteps(),
          })}
        </p>

        {/* 每个步骤都渲染选项标签和用户输入框 */}
        {renderTagSelection()}
        {renderUserInput()}

        {/* 底部按钮 */}
        <div className="flex gap-4 w-full justify-between">
          <Button variant="outline" onClick={handlePrevious} className="flex items-center gap-2">
            <ArrowLeft size={16} />
            {tCommon('previous')}
          </Button>

          {isLastStep() ? (
            <Button onClick={handleSubmit} disabled={!canProceed()} className="px-8">
              {t('start')}
            </Button>
          ) : (
            <Button
              onClick={handleNext}
              disabled={!canProceed()}
              className="px-8 flex items-center gap-2"
            >
              {tCommon('next')}
              <ArrowRight size={16} />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
