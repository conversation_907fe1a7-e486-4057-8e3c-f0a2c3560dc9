'use client'

import { useEffect, useState, useMemo, Suspense } from 'react'
import { notFound } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useSession } from '@/components/providers/session-provider'
import { ChatErrorBoundary } from '@/components/error/chat-error-boundary'
import Loading from '@/app/loading'
import dynamic from 'next/dynamic'

// 懒加载重型组件 - 关键优化
const ChatContainer = dynamic(() => import('@/components/chat/chat-container'), {
  loading: () => (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <Loading />
        <p className="mt-2 text-sm text-gray-600">加载聊天界面...</p>
      </div>
    </div>
  ),
  ssr: false, // 禁用SSR以加快初始加载
})

// 优化的Chat Hooks - 合并状态管理
import { useOptimizedChat } from '@/hooks/use-optimized-chat'

interface ChatPageProps {
  params: Promise<{ groupId: string }>
}

// 页面状态简化
type PageState = 
  | { status: 'parsing' }
  | { status: 'ready'; groupId: string }
  | { status: 'error'; error: string }

/**
 * 优化后的动态群聊页面
 * 主要优化：
 * 1. 延迟Socket.IO连接，不阻塞首次渲染
 * 2. 懒加载重型组件
 * 3. 简化状态管理
 * 4. 减少不必要的重渲染
 */
export default function ChatPageOptimized({ params }: ChatPageProps) {
  const [pageState, setPageState] = useState<PageState>({ status: 'parsing' })
  const t = useTranslations('chat')
  const { user, organization, isAuthenticated } = useSession()

  // 使用优化后的Chat Hook - 延迟初始化
  const {
    connectionStatus,
    error: chatError,
    isConnected,
    hasActiveSession,
    currentSession,
    initializeChat,
    reconnect
  } = useOptimizedChat()

  // 参数解析 - 简化逻辑
  useEffect(() => {
    let cancelled = false

    const parseParams = async () => {
      try {
        const { groupId } = await params
        
        if (cancelled) return
        
        // 基本验证
        if (!groupId || groupId.trim() === '') {
          setPageState({ status: 'error', error: 'Invalid group ID' })
          return
        }

        setPageState({ status: 'ready', groupId })
      } catch (error) {
        if (!cancelled) {
          console.error('参数解析失败:', error)
          notFound()
        }
      }
    }

    parseParams()
    return () => { cancelled = true }
  }, [params])

  // 延迟初始化聊天 - 在页面ready后再初始化Socket连接
  useEffect(() => {
    if (
      pageState.status === 'ready' && 
      user && 
      organization && 
      isAuthenticated &&
      !hasActiveSession // 避免重复初始化
    ) {
      // 使用微任务延迟执行，让页面首先渲染
      Promise.resolve().then(() => {
        initializeChat({
          groupId: pageState.groupId,
          userId: user.id || 'anonymous',
          organizationId: organization.id || 'default'
        })
      })
    }
  }, [pageState, user, organization, isAuthenticated, hasActiveSession, initializeChat])

  // 缓存渲染数据
  const renderData = useMemo(() => {
    if (pageState.status !== 'ready') {
      return { shouldRenderChat: false, groupId: null }
    }

    return {
      shouldRenderChat: isAuthenticated && user && organization,
      groupId: pageState.groupId
    }
  }, [pageState, isAuthenticated, user, organization])

  // 渲染优化 - 减少条件分支
  if (pageState.status === 'parsing') {
    return (
      <div className="flex items-center justify-center h-full">
        <Loading />
      </div>
    )
  }

  if (pageState.status === 'error') {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center p-6 max-w-md">
          <h3 className="text-lg font-medium text-gray-900 mb-2">页面加载失败</h3>
          <p className="text-sm text-gray-600 mb-4">{pageState.error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  // 认证检查
  if (!isAuthenticated || !user || !organization) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loading />
          <p className="mt-2 text-sm text-gray-600">验证用户信息...</p>
        </div>
      </div>
    )
  }

  // 连接错误处理
  if (connectionStatus === 'error' && chatError) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center p-6 max-w-md">
          <div className="text-red-500 mb-4">
            <svg
              className="mx-auto h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.96-.833-2.73 0L3.084 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{t('error.title')}</h3>
          <p className="text-sm text-gray-600 mb-4">{chatError}</p>
          <button
            onClick={reconnect}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            {t('error.reconnect')}
          </button>
        </div>
      </div>
    )
  }

  // 主要渲染逻辑 - 简化状态检查
  return (
    <ChatErrorBoundary
      onError={(errorInfo) => {
        console.error('聊天系统错误:', errorInfo)
      }}
      maxRetries={3}
    >
      <div className="flex flex-col h-full">
        <Suspense 
          fallback={
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Loading />
                <p className="mt-2 text-sm text-gray-600">初始化聊天...</p>
              </div>
            </div>
          }
        >
          <ChatContainer
            groupChatId={renderData.groupId!}
            userId={user.id || 'anonymous'}
            organizationId={organization.id || 'default'}
            // 外观配置
            appearance={{
              showAvatar: true,
              showTimestamp: false,
              enableCopy: true
            }}
            // 功能配置 - 禁用自动连接，由我们手动控制
            features={{
              autoConnect: false, // 重要：禁用自动连接
              enableFileUpload: false
            }}
            className="flex-1"
            onError={(error) => {
              console.error('ChatContainer错误:', error)
            }}
          />
        </Suspense>
      </div>
    </ChatErrorBoundary>
  )
}