/**
 * 消息格式适配器
 * 
 * 功能：
 * 1. 前端BaseWebSocketMessage <-> 后端Socket.IO事件格式的双向转换
 * 2. 后端事件类型到前端Payload类型的映射
 * 3. 消息内容提取和格式化
 * 4. 类型安全的消息验证
 */

import type {
  BaseWebSocketMessage,
  MessagePayload,
  StreamingPayload,
  CheckpointPayload,
  ReportPayload,
  ErrorPayload,
  FormField,
  FormFieldOption,
} from '@/types/websocket-event-type'

import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

import { NamingAdapter } from './naming-adapter'

// ============================================================================
// 后端事件类型映射
// ============================================================================

/**
 * 后端Socket.IO事件到前端Payload类型的映射
 */
const BACKEND_EVENT_TO_PAYLOAD_TYPE: Record<string, MessagePayload['type']> = {
  // 连接相关
  'message': 'streaming',           // 连接确认等普通消息
  
  // 处理状态 -> 流式输出（模拟进度更新）
  'processing_status': 'streaming',
  
  // 分析结果 -> 报告
  'analysis_result': 'report',
  
  // 用户交互需求 -> 表单
  'interaction_required': 'checkpoint',
  
  // 最终结果 -> 报告
  'final_result': 'report',
  
  // 闲聊响应 -> 流式输出
  'chat_response': 'streaming',
  
  // 总结结果 -> 报告  
  'summary_result': 'report',
  
  // 错误信息 -> 错误
  'error': 'error',
}

// ============================================================================
// ID生成工具  
// ============================================================================

/**
 * 生成唯一的消息ID
 */
function generateUniqueId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 获取当前时间戳
 */
function getCurrentTimestamp(): string {
  return new Date().toISOString()
}

// ============================================================================
// 上下文信息管理
// ============================================================================

/**
 * 上下文信息缓存（用于消息转换时的必要信息）
 */
class MessageContext {
  private static currentUserId: string | undefined
  private static currentGroupChatId: string | undefined
  private static currentOrganizationId: string | undefined
  private static currentSessionId: string | undefined

  static setContext(context: {
    userId?: string
    groupChatId?: string
    organizationId?: string
    sessionId?: string
  }) {
    this.currentUserId = context.userId
    this.currentGroupChatId = context.groupChatId
    this.currentOrganizationId = context.organizationId
    this.currentSessionId = context.sessionId
  }

  static getContext() {
    return {
      userId: this.currentUserId,
      groupChatId: this.currentGroupChatId,
      organizationId: this.currentOrganizationId,
      sessionId: this.currentSessionId,
    }
  }

  static clear() {
    this.currentUserId = undefined
    this.currentGroupChatId = undefined
    this.currentOrganizationId = undefined
    this.currentSessionId = undefined
  }
}

// ============================================================================
// 消息格式适配器类
// ============================================================================

export class MessageAdapter {
  
  // ============================================================================
  // 上下文管理
  // ============================================================================
  
  /**
   * 设置消息转换的上下文信息
   */
  static setContext(context: {
    userId?: string
    groupChatId?: string
    organizationId?: string
    sessionId?: string
  }) {
    MessageContext.setContext(context)
  }

  /**
   * 清除上下文信息
   */
  static clearContext() {
    MessageContext.clear()
  }

  // ============================================================================
  // 发送消息适配：前端 -> 后端
  // ============================================================================

  /**
   * 将前端BaseWebSocketMessage转换为后端接受的格式
   */
  static adaptOutgoingMessage(message: BaseWebSocketMessage) {
    // 提取消息文本内容
    const messageText = this.extractMessageContent(message.payload)
    
    // 构建后端格式（使用snake_case）
    const backendMessage = {
      message: messageText,
      session_id: message.sessionId,
      payload: message.payload, // 保留原始payload结构
      // 添加额外的消息元数据
      message_id: message.id,
      timestamp: message.timestamp,
    }

    // 使用命名适配器转换字段名
    return NamingAdapter.toSnakeCase(backendMessage)
  }

  /**
   * 从payload中提取消息文本内容
   */
  private static extractMessageContent(payload: MessagePayload): string {
    if (isStreamingPayload(payload)) {
      return payload.delta
    } else if (isCheckpointPayload(payload)) {
      return `用户交互请求：${payload.fields.length}个字段`
    } else if (isReportPayload(payload)) {
      return payload.content
    } else if (isErrorPayload(payload)) {
      return payload.message
    }
    
    return '未知消息类型'
  }

  // ============================================================================
  // 接收消息适配：后端 -> 前端
  // ============================================================================

  /**
   * 将后端Socket.IO事件转换为前端BaseWebSocketMessage格式
   */
  static adaptIncomingMessage(event: string, data: any): BaseWebSocketMessage {
    const context = MessageContext.getContext()
    
    // 构建标准的BaseWebSocketMessage
    const message: BaseWebSocketMessage = {
      id: generateUniqueId(),
      
      // 使用上下文信息或从数据中提取（优先使用上下文）
      groupChatId: context.groupChatId || data.group_chat_id || 'unknown',
      sessionId: context.sessionId || data.session_id || 'unknown',
      userId: context.userId || data.user_id || 'system',
      organizationId: context.organizationId || data.organization_id || 'unknown',
      
      // 转换事件到Payload
      payload: this.adaptEventToPayload(event, data),
      
      // 时间戳
      timestamp: getCurrentTimestamp(),
      
      // 消息状态
      status: this.determineMessageStatus(event) || 'delivered',
    }

    return message
  }

  /**
   * 将后端事件转换为前端Payload
   */
  private static adaptEventToPayload(event: string, data: any): MessagePayload {
    const payloadType = BACKEND_EVENT_TO_PAYLOAD_TYPE[event] || 'streaming'
    
    switch (payloadType) {
      case 'streaming':
        return this.createStreamingPayload(event, data)
        
      case 'checkpoint':
        return this.createCheckpointPayload(event, data)
        
      case 'report':
        return this.createReportPayload(event, data)
        
      case 'error':
        return this.createErrorPayload(event, data)
        
      default:
        // 默认作为流式消息处理
        return this.createStreamingPayload(event, data)
    }
  }

  /**
   * 创建流式Payload
   */
  private static createStreamingPayload(event: string, data: any): StreamingPayload {
    let delta = ''
    let isComplete = false

    switch (event) {
      case 'processing_status':
        delta = data.message || '处理中...'
        isComplete = data.status === 'completed'
        break
        
      case 'chat_response':
        delta = data.message || ''
        isComplete = true
        break
        
      case 'message':
        // 连接确认等消息
        delta = data.message || '已连接'
        isComplete = true
        break
        
      default:
        delta = data.message || JSON.stringify(data)
        isComplete = true
    }

    return {
      type: 'streaming',
      delta,
      isComplete,
    }
  }

  /**
   * 创建表单Payload
   */
  private static createCheckpointPayload(event: string, data: any): CheckpointPayload {
    let fields: FormField[] = []

    if (event === 'interaction_required' && data.options) {
      // 将后端的选项转换为表单字段
      fields = [{
        id: data.step || 'selection',
        label: data.message || '请选择',
        type: 'radio',
        required: true,
        defaultValue: null,
        options: data.options.map((option: any): FormFieldOption => ({
          label: option.name || option.label || String(option.id),
          value: option.id || option.value || option.name,
        })),
      }]
    }

    return {
      type: 'checkpoint',
      fields,
    }
  }

  /**
   * 创建报告Payload
   */
  private static createReportPayload(event: string, data: any): ReportPayload {
    let content = ''

    switch (event) {
      case 'analysis_result':
        content = data.data?.result || data.message || '分析完成'
        break
        
      case 'final_result':
        content = data.data?.report || data.message || '任务完成'
        break
        
      case 'summary_result':
        content = data.data?.summary || data.message || '总结完成'
        break
        
      default:
        content = data.message || JSON.stringify(data)
    }

    return {
      type: 'report',
      content,
    }
  }

  /**
   * 创建错误Payload
   */
  private static createErrorPayload(event: string, data: any): ErrorPayload {
    return {
      type: 'error',
      code: data.code || 'UNKNOWN_ERROR',
      message: data.message || '发生未知错误',
    }
  }

  /**
   * 根据事件类型确定消息状态
   */
  private static determineMessageStatus(event: string): BaseWebSocketMessage['status'] {
    switch (event) {
      case 'processing_status':
        return 'sending' // 处理中
      case 'error':
        return 'failed'  // 错误
      default:
        return 'delivered' // 已送达
    }
  }

  // ============================================================================
  // 用户交互响应处理
  // ============================================================================

  /**
   * 构建用户交互响应消息（发送给后端）
   */
  static createUserInteractionMessage(interactionData: Record<string, any> & {
    step: string
    selection: string | any
    sessionId?: string
  }) {
    const context = MessageContext.getContext()
    
    // 构建后端期望的格式
    const backendMessage = {
      step: interactionData.step,
      selection: interactionData.selection,
      session_id: context.sessionId || interactionData.sessionId,
      // 包含其他交互数据
      ...Object.fromEntries(
        Object.entries(interactionData).filter(([key]) => 
          !['step', 'selection', 'sessionId'].includes(key)
        )
      )
    }

    return NamingAdapter.toSnakeCase(backendMessage)
  }

  // ============================================================================
  // 消息验证工具
  // ============================================================================

  /**
   * 验证前端消息格式
   */
  static validateFrontendMessage(message: any): message is BaseWebSocketMessage {
    return !!(
      message &&
      typeof message.id === 'string' &&
      typeof message.groupChatId === 'string' &&
      typeof message.sessionId === 'string' &&
      typeof message.userId === 'string' &&
      typeof message.organizationId === 'string' &&
      message.payload &&
      typeof message.payload.type === 'string' &&
      typeof message.timestamp === 'string'
    )
  }

  /**
   * 验证后端事件数据
   */
  static validateBackendEventData(event: string, data: any): boolean {
    // 基本验证：事件名和数据存在
    if (!event || !data) {
      return false
    }

    // 根据事件类型进行特定验证
    switch (event) {
      case 'processing_status':
        return typeof data.message === 'string'
        
      case 'interaction_required':
        return !!(data.step && data.options && Array.isArray(data.options))
        
      case 'error':
        return typeof data.message === 'string'
        
      default:
        return true // 其他事件类型暂时通过
    }
  }

  // ============================================================================
  // 调试和诊断工具
  // ============================================================================

  /**
   * 生成消息转换的调试信息
   */
  static generateDebugInfo(event: string, data: any) {
    const context = MessageContext.getContext()
    const adaptedMessage = this.adaptIncomingMessage(event, data)
    
    return {
      backendEvent: {
        event,
        data,
      },
      context,
      frontendMessage: adaptedMessage,
      payloadType: adaptedMessage.payload.type,
      mappingUsed: BACKEND_EVENT_TO_PAYLOAD_TYPE[event],
      timestamp: getCurrentTimestamp(),
    }
  }

  /**
   * 获取支持的后端事件列表
   */
  static getSupportedBackendEvents(): string[] {
    return Object.keys(BACKEND_EVENT_TO_PAYLOAD_TYPE)
  }

  /**
   * 获取事件映射配置
   */
  static getEventMappingConfig() {
    return { ...BACKEND_EVENT_TO_PAYLOAD_TYPE }
  }
}

// ============================================================================
// 便捷导出函数
// ============================================================================

/**
 * 快捷发送消息转换
 */
export const adaptOutgoing = (message: BaseWebSocketMessage) => 
  MessageAdapter.adaptOutgoingMessage(message)

/**
 * 快捷接收消息转换
 */
export const adaptIncoming = (event: string, data: any): BaseWebSocketMessage => 
  MessageAdapter.adaptIncomingMessage(event, data)

/**
 * 快捷交互消息创建
 */
export const createInteraction = (interactionData: Record<string, any> & {
  step: string
  selection: any
  sessionId?: string
}) => 
  MessageAdapter.createUserInteractionMessage(interactionData)

/**
 * 设置消息上下文
 */
export const setMessageContext = (context: {
  userId?: string
  groupChatId?: string
  organizationId?: string
  sessionId?: string
}) => MessageAdapter.setContext(context)