/**
 * Chat系统的统一Hook - 封装所有chat相关功能
 * 提供简洁的API给组件使用
 */

import { useCallback, useEffect } from 'react'
import { useChatStore } from '@/stores/chat-store'
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

export interface ChatHookReturn {
  // 连接状态
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'
  isConnected: boolean
  isLoading: boolean
  error: string | null
  
  // 会话数据
  currentSession: any
  messages: BaseWebSocketMessage[]
  
  // 输入状态
  inputText: string
  isSending: boolean
  isInputDisabled: boolean
  
  // 操作方法
  connect: (auth: { userId: string; groupChatId: string; organizationId: string }) => Promise<void>
  disconnect: () => void
  sendMessage: (message: string) => Promise<void>
  sendInteraction: (step: string, selection: string) => Promise<void>
  setInputText: (text: string) => void
  createSession: (groupChatId: string, userId: string, organizationId: string) => string
  connectToGroup: (params: { groupId: string; userId: string; organizationId: string }) => Promise<void>
  reset: () => void
}

/**
 * 主要的Chat Hook
 * 提供chat系统所需的所有功能
 */
export function useChat(): ChatHookReturn {
  const {
    sessions,
    currentSessionId,
    connection,
    isLoading,
    isSending,
    error,
    inputText,
    isInputDisabled,
    connect,
    disconnect,
    sendMessage,
    sendInteraction,
    setInputText,
    createSession,
    reset,
  } = useChatStore()

  const connectionStatus = connection.status
  const isConnected = connectionStatus === 'connected'
  const currentSession = currentSessionId ? sessions[currentSessionId] : null
  const messages = currentSession?.messages || []

  // 创建连接到群组的便捷方法
  const connectToGroup = useCallback(async (params: { groupId: string; userId: string; organizationId: string }) => {
    // 创建或获取会话
    const sessionId = createSession(params.groupId, params.userId, params.organizationId)
    
    // 连接Socket.IO
    await connect({
      userId: params.userId,
      groupChatId: params.groupId,
      organizationId: params.organizationId,
    })
  }, [createSession, connect])

  return {
    // 连接状态
    connectionStatus,
    isConnected,
    isLoading,
    error,
    
    // 会话数据
    currentSession,
    messages,
    
    // 输入状态
    inputText,
    isSending,
    isInputDisabled,
    
    // 操作方法
    connect,
    disconnect,
    sendMessage,
    sendInteraction,
    setInputText,
    createSession,
    connectToGroup,
    reset,
  }
}

/**
 * 连接管理Hook
 * 专门处理Socket.IO连接相关功能
 */
export function useSocket() {
  const { connect, disconnect, connection, error } = useChatStore()

  const connectionStatus = connection.status
  const isConnected = connectionStatus === 'connected'
  const isConnecting = connectionStatus === 'connecting'
  const isReconnecting = connectionStatus === 'reconnecting'

  return {
    connect,
    disconnect,
    connectionStatus,
    isConnected,
    isConnecting,
    isReconnecting,
    error: connection.error || error,
    reconnectAttempts: connection.reconnectAttempts,
    maxReconnectAttempts: connection.maxReconnectAttempts,
  }
}

/**
 * 消息管理Hook
 * 专门处理消息发送和接收
 */
export function useChatMessages() {
  const { sessions, currentSessionId, sendMessage, sendInteraction, addMessage, updateMessage } = useChatStore()
  
  const currentSession = currentSessionId ? sessions[currentSessionId] : null
  const messages = currentSession?.messages || []
  
  // 发送文本消息
  const handleSendMessage = useCallback(async (text: string) => {
    if (!text.trim()) return
    
    try {
      await sendMessage(text.trim())
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }, [sendMessage])

  // 发送交互响应
  const handleSendInteraction = useCallback(async (step: string, selection: string) => {
    try {
      await sendInteraction(step, selection)
    } catch (error) {
      console.error('Failed to send interaction:', error)
      throw error
    }
  }, [sendInteraction])

  return {
    messages,
    sendMessage: handleSendMessage,
    sendInteraction: handleSendInteraction,
    addMessage,
    updateMessage,
  }
}

/**
 * 会话管理Hook
 * 处理chat会话的创建和切换
 */
export function useChatSession() {
  const { sessions, currentSessionId, groupToSessionMap, createSession, setCurrentSession } = useChatStore()
  
  const currentSession = currentSessionId ? sessions[currentSessionId] : null
  const hasActiveSession = !!currentSession

  // 获取或创建会话
  const getOrCreateSession = useCallback((
    groupChatId: string, 
    userId: string, 
    organizationId: string
  ) => {
    // 检查是否已存在会话
    const existingSessionId = groupToSessionMap[groupChatId]
    if (existingSessionId && sessions[existingSessionId]) {
      setCurrentSession(existingSessionId)
      return existingSessionId
    }

    // 创建新会话
    return createSession(groupChatId, userId, organizationId)
  }, [groupToSessionMap, sessions, createSession, setCurrentSession])

  return {
    currentSession,
    hasActiveSession,
    sessions,
    groupToSessionMap,
    createSession,
    setCurrentSession,
    getOrCreateSession,
  }
}

/**
 * 自动连接Hook
 * 在组件挂载时自动连接到指定群聊
 */
export function useAutoConnect(
  groupChatId: string,
  userId: string,
  organizationId: string,
  autoConnect = true
) {
  const { connect, connectionStatus } = useSocket()
  const { getOrCreateSession } = useChatSession()

  useEffect(() => {
    console.log('🔎 useAutoConnect effect triggered:', {
      autoConnect,
      groupChatId,
      userId,
      organizationId,
      connectionStatus
    })

    if (!autoConnect || !groupChatId || !userId || !organizationId) {
      console.log('⚠️ useAutoConnect: Missing required parameters')
      return
    }

    // 如果已经连接到相同的群聊，不需要重新连接
    if (connectionStatus === 'connected') {
      console.log('🔗 Already connected, ensuring session exists')
      // 确保会话已创建
      getOrCreateSession(groupChatId, userId, organizationId)
      return
    }

    // 自动连接
    const connectToChat = async () => {
      try {
        console.log('🚀 Starting auto connect process...')
        // 创建或获取会话
        const sessionId = getOrCreateSession(groupChatId, userId, organizationId)
        console.log('📁 Session created/found:', sessionId)
        
        // 连接Socket.IO
        console.log('🔌 Connecting to Socket.IO...')
        await connect({
          userId,
          groupChatId,
          organizationId,
        })
        console.log('✅ Socket.IO connection initiated')
      } catch (error) {
        console.error('❌ Auto connect failed:', error)
      }
    }

    connectToChat()
  }, [
    groupChatId, 
    userId, 
    organizationId, 
    autoConnect, 
    connect, 
    connectionStatus, 
    getOrCreateSession
  ])

  console.log('📊 useAutoConnect status:', {
    connectionStatus,
    isConnected: connectionStatus === 'connected'
  })

  return {
    connectionStatus,
    isConnected: connectionStatus === 'connected',
  }
}

/**
 * 消息过滤Hook
 * 根据不同条件过滤消息
 */
export function useFilteredMessages(filter?: {
  type?: 'streaming' | 'checkpoint' | 'report' | 'error'
  userId?: string
  dateRange?: { start: Date; end: Date }
}) {
  const { messages } = useChatMessages()

  const filteredMessages = messages.filter(message => {
    if (filter?.type && message.payload.type !== filter.type) {
      return false
    }
    
    if (filter?.userId && message.userId !== filter.userId) {
      return false
    }
    
    if (filter?.dateRange) {
      const messageTime = new Date(message.timestamp)
      if (messageTime < filter.dateRange.start || messageTime > filter.dateRange.end) {
        return false
      }
    }
    
    return true
  })

  return filteredMessages
}