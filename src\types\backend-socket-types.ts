/**
 * 后端Socket.IO接口类型定义
 * 
 * 设计原则：
 * 1. 定义后端Socket.IO事件的数据结构（使用snake_case）
 * 2. 提供类型安全的后端通信接口
 * 3. 与前端BaseWebSocketMessage形成对应关系
 * 4. 支持运行时类型验证
 */

// ============================================================================
// 后端认证数据类型（snake_case）
// ============================================================================

/**
 * 后端Socket.IO连接认证数据
 */
export interface BackendAuthData {
  user_id: string
  group_chat_id: string
  organization_id: string
  token?: string
}

// ============================================================================
// 后端消息数据类型
// ============================================================================

/**
 * 发送给后端的用户消息格式
 */
export interface BackendUserMessage {
  message: string
  session_id: string
  payload?: any
  message_id?: string
  timestamp?: string
}

/**
 * 发送给后端的用户交互数据
 */
export interface BackendUserInteraction {
  step: string
  selection: string | any
  session_id?: string
  [key: string]: any
}

// ============================================================================
// 后端事件数据类型（按事件分类）
// ============================================================================

/**
 * 连接确认消息
 */
export interface BackendConnectionMessage {
  status: string
  room?: string
  user_id?: string
  message?: string
}

/**
 * 处理状态更新
 */
export interface BackendProcessingStatus {
  step: string
  message: string
  status: 'processing' | 'completed' | 'failed'
  progress?: number
  data?: Record<string, any>
}

/**
 * 分析结果数据
 */
export interface BackendAnalysisResult {
  step: string
  message: string
  status: 'completed'
  data: {
    analysis_type: string
    result: string
    details?: Record<string, any>
  }
}

/**
 * 用户交互需求
 */
export interface BackendInteractionRequired {
  step: string
  message: string
  options: BackendInteractionOption[]
  session_id?: string
}

/**
 * 交互选项定义
 */
export interface BackendInteractionOption {
  id: string
  name: string
  label?: string
  value?: any
  description?: string
}

/**
 * 最终结果数据
 */
export interface BackendFinalResult {
  step: string
  message: string
  status: 'completed'
  data: {
    analysis_type?: string
    report: string
    summary?: string
    metadata?: Record<string, any>
  }
}

/**
 * 闲聊响应
 */
export interface BackendChatResponse {
  step: string
  message: string
  status: 'completed'
  response_type?: 'casual' | 'informative'
}

/**
 * 总结结果
 */
export interface BackendSummaryResult {
  step: string
  message: string
  status: 'completed'
  data: {
    summary: string
    key_points?: string[]
    metadata?: Record<string, any>
  }
}

/**
 * 错误信息
 */
export interface BackendErrorMessage {
  code?: string
  message: string
  details?: Record<string, any>
  session_id?: string
}

// ============================================================================
// 后端事件类型映射
// ============================================================================

/**
 * 后端Socket.IO事件类型枚举
 */
export enum BackendSocketEvent {
  // 连接相关
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  CONNECT_ERROR = 'connect_error',
  
  // 消息收发
  MESSAGE = 'message',
  USER_MESSAGE = 'user_message',
  USER_INTERACTION = 'user_interaction',
  
  // 业务事件
  PROCESSING_STATUS = 'processing_status',
  ANALYSIS_RESULT = 'analysis_result',
  INTERACTION_REQUIRED = 'interaction_required',
  FINAL_RESULT = 'final_result',
  CHAT_RESPONSE = 'chat_response',
  SUMMARY_RESULT = 'summary_result',
  
  // 错误处理
  ERROR = 'error',
}

/**
 * 后端事件数据类型映射
 */
export type BackendEventDataMap = {
  [BackendSocketEvent.MESSAGE]: BackendConnectionMessage
  [BackendSocketEvent.USER_MESSAGE]: BackendUserMessage
  [BackendSocketEvent.USER_INTERACTION]: BackendUserInteraction
  [BackendSocketEvent.PROCESSING_STATUS]: BackendProcessingStatus
  [BackendSocketEvent.ANALYSIS_RESULT]: BackendAnalysisResult
  [BackendSocketEvent.INTERACTION_REQUIRED]: BackendInteractionRequired
  [BackendSocketEvent.FINAL_RESULT]: BackendFinalResult
  [BackendSocketEvent.CHAT_RESPONSE]: BackendChatResponse
  [BackendSocketEvent.SUMMARY_RESULT]: BackendSummaryResult
  [BackendSocketEvent.ERROR]: BackendErrorMessage
}

// ============================================================================
// 通用后端消息包装器
// ============================================================================

/**
 * 通用后端消息结构
 */
export interface BackendMessage<T = any> {
  event: BackendSocketEvent | string
  data: T
  timestamp?: string
  session_id?: string
  user_id?: string
}

// ============================================================================
// 类型保护函数
// ============================================================================

/**
 * 验证后端认证数据
 */
export function isValidBackendAuth(data: any): data is BackendAuthData {
  return !!(
    data &&
    typeof data.user_id === 'string' &&
    typeof data.group_chat_id === 'string' &&
    typeof data.organization_id === 'string'
  )
}

/**
 * 验证后端用户消息
 */
export function isValidBackendUserMessage(data: any): data is BackendUserMessage {
  return !!(
    data &&
    typeof data.message === 'string' &&
    typeof data.session_id === 'string'
  )
}

/**
 * 验证后端处理状态
 */
export function isValidBackendProcessingStatus(data: any): data is BackendProcessingStatus {
  return !!(
    data &&
    typeof data.step === 'string' &&
    typeof data.message === 'string' &&
    typeof data.status === 'string' &&
    ['processing', 'completed', 'failed'].includes(data.status)
  )
}

/**
 * 验证后端交互需求
 */
export function isValidBackendInteractionRequired(data: any): data is BackendInteractionRequired {
  return !!(
    data &&
    typeof data.step === 'string' &&
    typeof data.message === 'string' &&
    Array.isArray(data.options) &&
    data.options.every((opt: any) => 
      typeof opt.id === 'string' && typeof opt.name === 'string'
    )
  )
}

/**
 * 验证后端错误消息
 */
export function isValidBackendError(data: any): data is BackendErrorMessage {
  return !!(
    data &&
    typeof data.message === 'string'
  )
}

// ============================================================================
// 事件验证器映射
// ============================================================================

/**
 * 事件数据验证器映射
 */
export const BackendEventValidators = {
  [BackendSocketEvent.MESSAGE]: (data: any) => typeof data === 'object',
  [BackendSocketEvent.USER_MESSAGE]: isValidBackendUserMessage,
  [BackendSocketEvent.PROCESSING_STATUS]: isValidBackendProcessingStatus,
  [BackendSocketEvent.INTERACTION_REQUIRED]: isValidBackendInteractionRequired,
  [BackendSocketEvent.ERROR]: isValidBackendError,
  // 其他事件使用通用验证
} as const

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 获取支持的后端事件列表
 */
export function getSupportedBackendEvents(): string[] {
  return Object.values(BackendSocketEvent)
}

/**
 * 检查是否为支持的后端事件
 */
export function isSupportedBackendEvent(event: string): event is BackendSocketEvent {
  return Object.values(BackendSocketEvent).includes(event as BackendSocketEvent)
}

/**
 * 验证后端事件数据
 */
export function validateBackendEventData(event: string, data: any): boolean {
  if (!isSupportedBackendEvent(event)) {
    return false
  }

  const validator = BackendEventValidators[event as keyof typeof BackendEventValidators]
  return validator ? validator(data) : true
}

/**
 * 创建标准后端消息
 */
export function createBackendMessage<T>(
  event: BackendSocketEvent | string,
  data: T,
  options?: {
    sessionId?: string
    userId?: string
  }
): BackendMessage<T> {
  const message: BackendMessage<T> = {
    event,
    data,
    timestamp: new Date().toISOString(),
  }
  
  if (options?.sessionId) {
    message.session_id = options.sessionId
  }
  if (options?.userId) {
    message.user_id = options.userId
  }
  
  return message
}

// ============================================================================
// 调试和诊断工具
// ============================================================================

/**
 * 生成后端事件的调试信息
 */
export function generateBackendEventDebugInfo(event: string, data: any) {
  return {
    event,
    data,
    isSupported: isSupportedBackendEvent(event),
    isValid: validateBackendEventData(event, data),
    eventType: isSupportedBackendEvent(event) ? (BackendSocketEvent as any)[event] || 'UNKNOWN' : 'UNKNOWN',
    timestamp: new Date().toISOString(),
  }
}

/**
 * 获取后端事件配置概览
 */
export function getBackendEventConfig() {
  return {
    supportedEvents: getSupportedBackendEvents(),
    eventCount: getSupportedBackendEvents().length,
    hasValidators: Object.keys(BackendEventValidators).length,
    eventTypes: {
      connection: [BackendSocketEvent.CONNECT, BackendSocketEvent.DISCONNECT, BackendSocketEvent.CONNECT_ERROR],
      messaging: [BackendSocketEvent.MESSAGE, BackendSocketEvent.USER_MESSAGE],
      interaction: [BackendSocketEvent.USER_INTERACTION, BackendSocketEvent.INTERACTION_REQUIRED],
      business: [
        BackendSocketEvent.PROCESSING_STATUS,
        BackendSocketEvent.ANALYSIS_RESULT,
        BackendSocketEvent.FINAL_RESULT,
        BackendSocketEvent.CHAT_RESPONSE,
        BackendSocketEvent.SUMMARY_RESULT,
      ],
      error: [BackendSocketEvent.ERROR],
    },
  }
}