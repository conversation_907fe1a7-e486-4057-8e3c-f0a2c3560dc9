"use client";

import { cn } from "@/lib/utils";
import { Globe, Lightbulb, Loader2, Mic, Paperclip, Send } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useEffect, useRef, useState } from "react";

// Default placeholders for general use
const DEFAULT_PLACEHOLDERS = [
  "Generate website with HextaUI",
  "Create a new project with Next.js",
  "What is the meaning of life?",
  "What is the best way to learn React?",
  "How to cook a delicious meal?",
  "Summarize this article",
];

// Chat-specific placeholders
const CHAT_PLACEHOLDERS = [
  "输入消息...",
  "有什么可以帮助你的吗？",
  "开始新的对话", 
  "问我任何问题",
  "让我们聊聊吧",
  "我在这里为你服务",
];

interface AIChatInputProps {
  onSendMessage?: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  placeholders?: string[];
  showAttachButton?: boolean;
  showVoiceButton?: boolean;
  showThinkButton?: boolean;
  showDeepSearchButton?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  maxRows?: number;
  minRows?: number;
  containerClassName?: string;
  multiline?: boolean;
  // 新增支持
  loading?: boolean;
  onAttachClick?: () => void;
  autoFocus?: boolean;
}

const AIChatInput: React.FC<AIChatInputProps> = ({
  onSendMessage = () => {},
  disabled = false,
  placeholder,
  placeholders,
  showAttachButton = true,
  showVoiceButton = true,
  showThinkButton = true,
  showDeepSearchButton = true,
  value,
  onChange,
  maxRows = 5,
  minRows = 1,
  containerClassName = "",
  multiline = false,
  // 新增支持
  loading = false,
  onAttachClick = () => {},
  autoFocus = false,
}) => {
  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  const [showPlaceholder, setShowPlaceholder] = useState(true);
  const [isActive, setIsActive] = useState(false);
  const [thinkActive, setThinkActive] = useState(false);
  const [deepSearchActive, setDeepSearchActive] = useState(false);
  
  // Use controlled or uncontrolled mode
  const [internalValue, setInternalValue] = useState("");
  const inputValue = value !== undefined ? value : internalValue;
  const setInputValue = (newValue: string) => {
    if (value !== undefined) {
      onChange?.(newValue);
    } else {
      setInternalValue(newValue);
    }
  };
  
  // Use provided placeholders or defaults
  const PLACEHOLDERS = placeholders || (showThinkButton || showDeepSearchButton ? DEFAULT_PLACEHOLDERS : CHAT_PLACEHOLDERS);
  
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Cycle placeholder text when input is inactive
  useEffect(() => {
    if (isActive || inputValue) return;

    const interval = setInterval(() => {
      setShowPlaceholder(false);
      setTimeout(() => {
        setPlaceholderIndex((prev) => (prev + 1) % PLACEHOLDERS.length);
        setShowPlaceholder(true);
      }, 400);
    }, 3000);

    return () => clearInterval(interval);
  }, [isActive, inputValue, PLACEHOLDERS.length]);

  // Close input when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        if (!inputValue) setIsActive(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [inputValue]);

  const handleActivate = () => {
    if (disabled) return;
    setIsActive(true);
  };

  const handleSend = () => {
    if (!inputValue.trim() || disabled) return;
    onSendMessage?.(inputValue);
    setInputValue("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !multiline) {
      e.preventDefault();
      handleSend();
    }
  };

  const containerVariants = {
    collapsed: {
      height: 68,
      boxShadow: "0 2px 8px 0 rgba(0,0,0,0.08)",
      transition: { type: "spring", stiffness: 120, damping: 18 },
    },
    expanded: {
      height: 128,
      boxShadow: "0 8px 32px 0 rgba(0,0,0,0.16)",
      transition: { type: "spring", stiffness: 120, damping: 18 },
    },
  };

  const placeholderContainerVariants = {
    initial: {},
    animate: { transition: { staggerChildren: 0.025 } },
    exit: { transition: { staggerChildren: 0.015, staggerDirection: -1 } },
  };

  const letterVariants = {
    initial: {
      opacity: 0,
      filter: "blur(12px)",
      y: 10,
    },
    animate: {
      opacity: 1,
      filter: "blur(0px)",
      y: 0,
      transition: {
        opacity: { duration: 0.25 },
        filter: { duration: 0.4 },
        y: { type: "spring", stiffness: 80, damping: 20 },
      },
    },
    exit: {
      opacity: 0,
      filter: "blur(12px)",
      y: -10,
      transition: {
        opacity: { duration: 0.2 },
        filter: { duration: 0.3 },
        y: { type: "spring", stiffness: 80, damping: 20 },
      },
    },
  };

  return (
    <div className={cn("w-full flex justify-center items-center text-black", containerClassName)}>
      <motion.div
        ref={wrapperRef}
        className="w-full max-w-3xl"
        variants={containerVariants as any}
        animate={isActive || inputValue ? "expanded" : "collapsed"}
        initial="collapsed"
        style={{ overflow: "hidden", borderRadius: 32, background: "#fff" }}
        onClick={handleActivate}
      >
        <div className="flex flex-col items-stretch w-full h-full">
          {/* Input Row */}
          <div className="flex items-center gap-2 p-3 rounded-full bg-white max-w-3xl w-full">
            {showAttachButton && (
              <button
                className={`p-3 rounded-full transition ${
                  disabled ? 'text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'
                }`}
                title="Attach file"
                type="button"
                tabIndex={-1}
                disabled={disabled}
                onClick={onAttachClick}
              >
                <Paperclip size={20} />
              </button>
            )}

            {/* Text Input & Placeholder */}
            <div className="relative flex-1">
              {multiline ? (
                <textarea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 border-0 outline-0 rounded-md py-2 text-base bg-transparent w-full font-normal resize-none"
                  style={{ position: "relative", zIndex: 1, minHeight: `${minRows * 1.5}rem`, maxHeight: `${maxRows * 1.5}rem` }}
                  rows={minRows}
                  disabled={disabled}
                  placeholder={placeholder}
                  onFocus={handleActivate}
                  autoFocus={autoFocus}
                />
              ) : (
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 border-0 outline-0 rounded-md py-2 text-base bg-transparent w-full font-normal"
                  style={{ position: "relative", zIndex: 1 }}
                  disabled={disabled}
                  placeholder={placeholder}
                  onFocus={handleActivate}
                  autoFocus={autoFocus}
                />
              )}
              {!placeholder && (
                <div className="absolute left-0 top-0 w-full h-full pointer-events-none flex items-center px-3 py-2">
                  <AnimatePresence mode="wait">
                    {showPlaceholder && !isActive && !inputValue && (
                      <motion.span
                        key={placeholderIndex}
                        className="absolute left-0 top-1/2 -translate-y-1/2 text-gray-400 select-none pointer-events-none"
                        style={{
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          zIndex: 0,
                        }}
                        variants={placeholderContainerVariants}
                        initial="initial"
                        animate="animate"
                        exit="exit"
                      >
                        {PLACEHOLDERS[placeholderIndex]
                          .split("")
                          .map((char, i) => (
                            <motion.span
                              key={i}
                              variants={letterVariants as any}
                              style={{ display: "inline-block" }}
                            >
                              {char === " " ? "\u00A0" : char}
                            </motion.span>
                          ))}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>

            {showVoiceButton && (
              <button
                className={`p-3 rounded-full transition ${
                  disabled ? 'text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'
                }`}
                title="Voice input"
                type="button"
                tabIndex={-1}
                disabled={disabled}
              >
                <Mic size={20} />
              </button>
            )}
            <button
              className={`flex items-center gap-1 p-3 rounded-full font-medium justify-center transition ${
                disabled || !inputValue.trim() || loading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-black hover:bg-zinc-700 text-white'
              }`}
              title={loading ? "发送中..." : "Send"}
              type="button"
              tabIndex={-1}
              disabled={disabled || !inputValue.trim() || loading}
              onClick={handleSend}
            >
              {loading ? (
                <Loader2 size={18} className="animate-spin" />
              ) : (
                <Send size={18} />
              )}
            </button>
          </div>

          {/* Expanded Controls */}
          {(showThinkButton || showDeepSearchButton) && (
            <motion.div
              className="w-full flex justify-start px-4 items-center text-sm"
              variants={{
                hidden: {
                  opacity: 0,
                  y: 20,
                  pointerEvents: "none" as const,
                  transition: { duration: 0.25 },
                },
                visible: {
                  opacity: 1,
                  y: 0,
                  pointerEvents: "auto" as const,
                  transition: { duration: 0.35, delay: 0.08 },
                },
              }}
              initial="hidden"
              animate={isActive || inputValue ? "visible" : "hidden"}
              style={{ marginTop: 8 }}
            >
              <div className="flex gap-3 items-center">
                {/* Think Toggle */}
                {showThinkButton && (
                  <button
                    className={`flex items-center gap-1 px-4 py-2 rounded-full transition-all font-medium group ${
                      disabled
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : thinkActive
                        ? "bg-blue-600/10 outline outline-blue-600/60 text-blue-950"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                    title="Think"
                    type="button"
                    disabled={disabled}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!disabled) {
                        setThinkActive((a) => !a);
                      }
                    }}
                  >
                    <Lightbulb
                      className={`transition-all ${
                        disabled ? '' : 'group-hover:fill-yellow-300'
                      }`}
                      size={18}
                    />
                    Think
                  </button>
                )}

                {/* Deep Search Toggle */}
                {showDeepSearchButton && (
                  <motion.button
                    className={`flex items-center px-4 gap-1 py-2 rounded-full transition font-medium whitespace-nowrap overflow-hidden justify-start ${
                      disabled
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : deepSearchActive
                        ? "bg-blue-600/10 outline outline-blue-600/60 text-blue-950"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    }`}
                    title="Deep Search"
                    type="button"
                    disabled={disabled}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!disabled) {
                        setDeepSearchActive((a) => !a);
                      }
                    }}
                    initial={false}
                    animate={{
                      width: deepSearchActive ? 125 : 36,
                      paddingLeft: deepSearchActive ? 8 : 9,
                    }}
                  >
                    <div className="flex-1">
                      <Globe size={18} />
                    </div>
                    <motion.span
                      className="pb-[2px]"
                      initial={false}
                      animate={{
                        opacity: deepSearchActive ? 1 : 0,
                      }}
                    >
                      Deep Search
                    </motion.span>
                  </motion.button>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export { AIChatInput };

