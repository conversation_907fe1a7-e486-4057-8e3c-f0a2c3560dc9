/**
 * 优化的标签选择组件
 * 性能优化：memo化、事件处理优化
 */

import { memo, useCallback } from 'react'
import { cn } from '@/lib/utils'

interface TagSelectionProps {
  options: string[]
  selectedOptions: string[]
  onTagClick: (tag: string) => void
  surveyQuestions: Record<string, { title: string; options: Record<string, string> }>
  currentField: string
  className?: string
}

const TagSelection = memo(function TagSelection({
  options,
  selectedOptions,
  onTagClick,
  surveyQuestions,
  currentField,
  className
}: TagSelectionProps) {
  // 缓存选项文本获取逻辑
  const getOptionText = useCallback((optionKey: string) => {
    const currentQuestion = surveyQuestions[currentField]
    return currentQuestion?.options[optionKey] || optionKey
  }, [surveyQuestions, currentField])

  // 优化的点击处理
  const handleTagClick = useCallback((optionKey: string) => {
    onTagClick(optionKey)
  }, [onTagClick])

  if (options.length === 0) {
    return null
  }

  return (
    <div className={cn("w-full", className)}>
      <div className="flex flex-wrap gap-3 justify-center">
        {options.map(optionKey => {
          const optionText = getOptionText(optionKey)
          const isSelected = selectedOptions.includes(optionKey)
          
          return (
            <button
              key={optionKey}
              onClick={() => handleTagClick(optionKey)}
              className={cn(
                'px-4 py-2 rounded-full text-sm font-medium transition-all cursor-pointer',
                isSelected
                  ? 'bg-blue-100 text-blue-700 border-2 border-blue-300'
                  : 'bg-gray-100 text-gray-700 border-2 border-transparent hover:bg-gray-200'
              )}
              type="button"
              aria-pressed={isSelected}
            >
              {optionText}
            </button>
          )
        })}
      </div>
    </div>
  )
})

TagSelection.displayName = 'TagSelection'

export default TagSelection