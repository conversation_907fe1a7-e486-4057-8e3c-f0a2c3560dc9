/**
 * 聊天连接Store - 专门处理Socket.IO连接
 * 从原chat-store中分离出来，减少单个store的复杂度
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'
import { io, Socket } from 'socket.io-client'

// ============================================================================
// 连接状态接口
// ============================================================================

export interface ConnectionState {
  socket: Socket | null
  status: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'
  error: string | null
  reconnectAttempts: number
  maxReconnectAttempts: number
  lastConnectedAt: number | null
  connectionId: string | null
}

export interface ConnectionActions {
  connect: (auth: { userId: string; groupChatId: string; organizationId: string }) => Promise<void>
  disconnect: () => void
  reconnect: () => Promise<void>
  clearError: () => void
  updateStatus: (status: ConnectionState['status']) => void
}

// ============================================================================
// Store实现
// ============================================================================

const initialState: ConnectionState = {
  socket: null,
  status: 'disconnected',
  error: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  lastConnectedAt: null,
  connectionId: null,
}

export const useChatConnectionStore = create<ConnectionState & ConnectionActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      connect: async (auth) => {
        console.log('🚀 Connection store: starting connection', auth)
        
        const { socket: existingSocket } = get()
        
        // 清理现有连接
        if (existingSocket) {
          console.log('🔌 Cleaning up existing socket')
          existingSocket.removeAllListeners()
          existingSocket.disconnect()
        }

        set((state) => {
          state.status = 'connecting'
          state.error = null
          state.connectionId = `conn_${Date.now()}_${Math.random().toString(36).slice(2)}`
        })

        try {
          const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:8000'
          console.log('🌍 Connecting to:', socketUrl)
          
          const socket = io(socketUrl, {
            auth: {
              user_id: auth.userId,
              group_chat_id: auth.groupChatId,
              organization_id: auth.organizationId,
            },
            transports: ['websocket', 'polling'],
            reconnection: true,
            reconnectionAttempts: get().maxReconnectAttempts,
            reconnectionDelay: 1000,
            timeout: 20000,
            forceNew: true,
          })

          // 设置基础事件监听器
          setupConnectionListeners(socket, set, get)

          set((state) => {
            // 使用类型断言避免immer类型问题
            ;(state as any).socket = socket
          })

          console.log('✅ Socket created and listeners attached')

        } catch (error) {
          console.error('❌ Connection failed:', error)
          set((state) => {
            state.status = 'error'
            state.error = error instanceof Error ? error.message : 'Connection failed'
          })
        }
      },

      disconnect: () => {
        console.log('🔌 Disconnecting socket')
        const { socket } = get()
        
        if (socket) {
          socket.removeAllListeners()
          socket.disconnect()
        }

        set((state) => {
          state.socket = null
          state.status = 'disconnected'
          state.error = null
          state.reconnectAttempts = 0
          state.lastConnectedAt = null
          state.connectionId = null
        })
      },

      reconnect: async () => {
        console.log('🔄 Manual reconnect triggered')
        const { socket } = get()
        
        if (socket) {
          socket.connect()
        } else {
          console.warn('⚠️ No socket to reconnect')
        }
      },

      clearError: () => {
        set((state) => {
          state.error = null
        })
      },

      updateStatus: (status) => {
        set((state) => {
          state.status = status
        })
      },
    }))
  )
)

// ============================================================================
// Socket事件监听器 - 仅处理连接相关事件
// ============================================================================

function setupConnectionListeners(
  socket: Socket,
  set: any,
  get: () => ConnectionState & ConnectionActions
) {
  // 连接成功
  socket.on('connect', () => {
    console.log('✅ Socket connected:', socket.id)
    set((state: ConnectionState) => {
      state.status = 'connected'
      state.error = null
      state.reconnectAttempts = 0
      state.lastConnectedAt = Date.now()
    })
  })

  // 连接失败
  socket.on('connect_error', (error) => {
    console.error('❌ Socket connection error:', error)
    set((state: ConnectionState) => {
      state.status = 'error'
      state.error = error.message || 'Connection failed'
    })
  })

  // 断开连接
  socket.on('disconnect', (reason) => {
    console.log('🔌 Socket disconnected:', reason)
    set((state: ConnectionState) => {
      state.status = 'disconnected'
      state.error = reason === 'io server disconnect' ? 'Server disconnected' : null
    })
  })

  // 重连尝试
  socket.on('reconnect_attempt', (attemptNumber) => {
    console.log('🔄 Reconnect attempt:', attemptNumber)
    set((state: ConnectionState) => {
      state.status = 'reconnecting'
      state.reconnectAttempts = attemptNumber
    })
  })

  // 重连成功
  socket.on('reconnect', () => {
    console.log('✅ Socket reconnected')
    set((state: ConnectionState) => {
      state.status = 'connected'
      state.error = null
      state.reconnectAttempts = 0
      state.lastConnectedAt = Date.now()
    })
  })

  // 重连失败
  socket.on('reconnect_failed', () => {
    console.error('❌ Socket reconnect failed')
    set((state: ConnectionState) => {
      state.status = 'error'
      state.error = 'Reconnection failed after maximum attempts'
    })
  })
}

// ============================================================================
// Selector Hooks
// ============================================================================

export const useConnectionStatus = () => {
  return useChatConnectionStore((state) => ({
    status: state.status,
    error: state.error,
    isConnected: state.status === 'connected',
    isConnecting: state.status === 'connecting',
    isReconnecting: state.status === 'reconnecting',
    reconnectAttempts: state.reconnectAttempts,
    maxReconnectAttempts: state.maxReconnectAttempts,
    lastConnectedAt: state.lastConnectedAt,
  }))
}

export const useSocket = () => {
  return useChatConnectionStore((state) => state.socket)
}