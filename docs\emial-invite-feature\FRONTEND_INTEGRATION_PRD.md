# 前端邮箱验证注册流程集成PRD

## 📋 文档概述

**目标**: 为前端开发团队提供完整的邮箱验证注册流程实现指导  
**范围**: 从 `/register` 页面到邮箱验证完成的完整用户旅程  
**技术栈**: React/Next.js + TypeScript + Tailwind CSS  
**更新时间**: 2025-01-31

---

## 🎯 核心目标

### 用户体验目标
- ✅ 用户在每个步骤都有明确的状态反馈和操作指引
- ✅ 邮件发送失败时用户能够自助解决问题
- ✅ 完整的错误处理和恢复机制
- ✅ 移动端友好的响应式设计

### 技术目标
- ✅ 与后端修复后的接口完美集成
- ✅ 类型安全的API调用和状态管理
- ✅ 可复用的UI组件和业务逻辑
- ✅ 优秀的性能和用户体验

---

## 🗺️ 完整用户流程设计

### 流程图
```mermaid
graph TD
    A[/register 注册页面] --> B[填写注册表单]
    B --> C[提交注册请求]
    C --> D{注册结果}
    
    D -->|成功 + 邮件发送成功| E[/email-verification-pending<br/>邮箱验证等待页面]
    D -->|成功 + 邮件发送失败| F[/email-verification-failed<br/>邮件发送失败页面]
    D -->|失败| G[显示错误信息<br/>停留在注册页面]
    
    E --> H[用户查收邮件]
    F --> I[点击重发邮件]
    I --> J{重发结果}
    J -->|成功| E
    J -->|失败| F
    
    H --> K[点击邮件中的验证链接]
    K --> L[/verify-email?token=xxx<br/>验证处理页面]
    L --> M{验证结果}
    
    M -->|成功| N[/verify-success<br/>验证成功页面]
    M -->|失败| O[/verify-failed<br/>验证失败页面]
    
    N --> P[自动跳转到登录页面]
    O --> Q[提供重发邮件选项]
    Q --> I
```

### 页面路由配置
```typescript
// pages/register.tsx - 注册页面
// pages/email-verification-pending.tsx - 邮箱验证等待页面
// pages/email-verification-failed.tsx - 邮件发送失败页面
// pages/verify-email.tsx - 邮箱验证处理页面
// pages/verify-success.tsx - 验证成功页面
// pages/verify-failed.tsx - 验证失败页面
```

---

## 🔌 API集成方案

### 1. 注册接口集成

#### 接口定义
```typescript
// types/auth.ts
interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  company_name: string;
  invite_code: string;
  language?: string;
}

interface RegisterResponse {
  success: boolean;
  message: string;
  user: {
    id: string;
    email: string;
    name: string;
    company_name: string;
    emailVerified: boolean;
  };
  nextSteps: {
    requireEmailVerification: boolean;
    verificationEmailSent: boolean;
    instructions: string;
    canResendEmail: boolean;
    resendEndpoint: string;
    emailError?: string;
    troubleshooting?: string;
  };
}

interface ApiError {
  error: string;
  message: string;
}
```

#### API调用实现
```typescript
// services/auth.ts
export class AuthService {
  private static baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:10086';

  static async register(data: RegisterRequest): Promise<RegisterResponse> {
    const response = await fetch(`${this.baseUrl}/api/auth/sign-up-invite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.message || '注册失败');
    }

    return response.json();
  }

  static async checkEmailVerification(email: string) {
    const response = await fetch(`${this.baseUrl}/api/auth/check-email-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.message || '查询失败');
    }

    return response.json();
  }

  static async resendVerificationEmail(email: string) {
    const response = await fetch(`${this.baseUrl}/api/auth/resend-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const error: ApiError = await response.json();
      throw new Error(error.message || '重发失败');
    }

    return response.json();
  }
}
```

### 2. 状态管理方案

#### 使用 Zustand 进行状态管理
```typescript
// stores/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  email: string;
  name: string;
  company_name: string;
  emailVerified: boolean;
}

interface AuthState {
  // 状态
  user: User | null;
  isLoading: boolean;
  error: string | null;
  registrationStep: 'form' | 'pending' | 'failed' | 'success';
  
  // 邮箱验证相关状态
  emailVerificationStatus: {
    required: boolean;
    sent: boolean;
    canResend: boolean;
    lastSentAt?: Date;
    cooldownSeconds?: number;
  };

  // Actions
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setRegistrationStep: (step: AuthState['registrationStep']) => void;
  updateEmailVerificationStatus: (status: Partial<AuthState['emailVerificationStatus']>) => void;
  reset: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      // 初始状态
      user: null,
      isLoading: false,
      error: null,
      registrationStep: 'form',
      emailVerificationStatus: {
        required: false,
        sent: false,
        canResend: false,
      },

      // Actions
      setUser: (user) => set({ user }),
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      setRegistrationStep: (registrationStep) => set({ registrationStep }),
      updateEmailVerificationStatus: (status) =>
        set((state) => ({
          emailVerificationStatus: { ...state.emailVerificationStatus, ...status },
        })),
      reset: () =>
        set({
          user: null,
          isLoading: false,
          error: null,
          registrationStep: 'form',
          emailVerificationStatus: {
            required: false,
            sent: false,
            canResend: false,
          },
        }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        emailVerificationStatus: state.emailVerificationStatus,
      }),
    }
  )
);
```

---

## 📱 页面实现方案

### 1. 注册页面 (`/register`)

#### 核心功能
- 表单验证和提交
- 实时邀请码验证
- 密码强度指示器
- 错误处理和用户反馈

#### 实现代码
```typescript
// pages/register.tsx
import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '../stores/authStore';
import { AuthService } from '../services/auth';
import { RegisterForm } from '../components/RegisterForm';
import { LoadingSpinner } from '../components/LoadingSpinner';
import { ErrorAlert } from '../components/ErrorAlert';

export default function RegisterPage() {
  const router = useRouter();
  const { setUser, setLoading, setError, setRegistrationStep, updateEmailVerificationStatus, isLoading, error } = useAuthStore();

  const handleRegister = async (formData: RegisterRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await AuthService.register(formData);

      // 保存用户信息
      setUser(response.user);

      // 更新邮箱验证状态
      updateEmailVerificationStatus({
        required: response.nextSteps.requireEmailVerification,
        sent: response.nextSteps.verificationEmailSent,
        canResend: response.nextSteps.canResendEmail,
        lastSentAt: new Date(),
      });

      // 根据邮件发送状态跳转到不同页面
      if (response.nextSteps.verificationEmailSent) {
        setRegistrationStep('pending');
        router.push('/email-verification-pending');
      } else {
        setRegistrationStep('failed');
        router.push('/email-verification-failed');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '注册失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900">创建您的账户</h1>
          <p className="text-gray-600 mt-2">加入 SpecificAI，开启智能办公新体验</p>
        </div>

        {error && <ErrorAlert message={error} onClose={() => setError(null)} />}

        <RegisterForm onSubmit={handleRegister} isLoading={isLoading} />

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            已有账户？
            <a href="/login" className="text-blue-600 hover:text-blue-500 ml-1">
              立即登录
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
```

### 2. 邮箱验证等待页面 (`/email-verification-pending`)

#### 核心功能
- 显示验证邮件发送成功状态
- 提供重发邮件功能（带冷却时间）
- 实时查询验证状态
- 用户指引和帮助信息

#### 实现代码
```typescript
// pages/email-verification-pending.tsx
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '../stores/authStore';
import { AuthService } from '../services/auth';
import { MailIcon, RefreshIcon, CheckCircleIcon } from '@heroicons/react/outline';

export default function EmailVerificationPendingPage() {
  const router = useRouter();
  const { user, emailVerificationStatus, updateEmailVerificationStatus } = useAuthStore();
  const [isResending, setIsResending] = useState(false);
  const [cooldownSeconds, setCooldownSeconds] = useState(0);
  const [isChecking, setIsChecking] = useState(false);

  // 冷却时间倒计时
  useEffect(() => {
    if (cooldownSeconds > 0) {
      const timer = setTimeout(() => setCooldownSeconds(cooldownSeconds - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [cooldownSeconds]);

  // 定期检查验证状态
  useEffect(() => {
    if (!user?.email) return;

    const checkStatus = async () => {
      try {
        setIsChecking(true);
        const response = await AuthService.checkEmailVerification(user.email);

        if (response.data.emailVerified) {
          // 邮箱已验证，跳转到成功页面
          router.push('/verify-success');
        }
      } catch (error) {
        console.error('检查验证状态失败:', error);
      } finally {
        setIsChecking(false);
      }
    };

    // 每30秒检查一次
    const interval = setInterval(checkStatus, 30000);
    return () => clearInterval(interval);
  }, [user?.email, router]);

  const handleResendEmail = async () => {
    if (!user?.email || cooldownSeconds > 0) return;

    try {
      setIsResending(true);
      await AuthService.resendVerificationEmail(user.email);

      updateEmailVerificationStatus({
        sent: true,
        lastSentAt: new Date(),
      });

      setCooldownSeconds(120); // 2分钟冷却时间
    } catch (error) {
      console.error('重发邮件失败:', error);
      // 可以显示错误提示
    } finally {
      setIsResending(false);
    }
  };

  if (!user) {
    router.push('/register');
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <MailIcon className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">验证您的邮箱地址</h1>
          <p className="text-gray-600">
            我们已向 <strong className="text-gray-900">{user.email}</strong> 发送了验证邮件
          </p>
        </div>

        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-900 mb-2">接下来怎么做？</h3>
          <ol className="text-sm text-blue-800 text-left space-y-1">
            <li>1. 查收您的邮箱（包括垃圾邮件文件夹）</li>
            <li>2. 点击邮件中的"验证邮箱"按钮</li>
            <li>3. 完成验证后即可正常使用账户</li>
          </ol>
        </div>

        <div className="space-y-4">
          <button
            onClick={handleResendEmail}
            disabled={cooldownSeconds > 0 || isResending}
            className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshIcon className={`w-4 h-4 mr-2 ${isResending ? 'animate-spin' : ''}`} />
            {cooldownSeconds > 0
              ? `重新发送邮件 (${cooldownSeconds}s)`
              : isResending
                ? '发送中...'
                : '重新发送邮件'
            }
          </button>

          <button
            onClick={() => router.push('/login')}
            className="w-full px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-500"
          >
            返回登录
          </button>
        </div>

        <div className="mt-6 text-xs text-gray-500">
          <p>没有收到邮件？请检查垃圾邮件文件夹，或
            <a href="/support" className="text-blue-600 hover:text-blue-500 ml-1">联系客服</a>
          </p>
          {isChecking && (
            <p className="mt-2 flex items-center justify-center">
              <RefreshIcon className="w-3 h-3 animate-spin mr-1" />
              正在检查验证状态...
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
```

### 3. 邮件发送失败页面 (`/email-verification-failed`)

#### 核心功能
- 显示邮件发送失败状态和错误信息
- 提供重试发送邮件功能
- 故障排除指引
- 联系客服选项

#### 实现代码
```typescript
// pages/email-verification-failed.tsx
import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '../stores/authStore';
import { AuthService } from '../services/auth';
import { ExclamationTriangleIcon, RefreshIcon } from '@heroicons/react/outline';

export default function EmailVerificationFailedPage() {
  const router = useRouter();
  const { user, updateEmailVerificationStatus } = useAuthStore();
  const [isRetrying, setIsRetrying] = useState(false);
  const [retrySuccess, setRetrySuccess] = useState(false);

  const handleRetryEmail = async () => {
    if (!user?.email) return;

    try {
      setIsRetrying(true);
      await AuthService.resendVerificationEmail(user.email);

      updateEmailVerificationStatus({
        sent: true,
        lastSentAt: new Date(),
      });

      setRetrySuccess(true);

      // 3秒后跳转到等待页面
      setTimeout(() => {
        router.push('/email-verification-pending');
      }, 3000);
    } catch (error) {
      console.error('重试发送邮件失败:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  if (!user) {
    router.push('/register');
    return null;
  }

  if (retrySuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 px-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircleIcon className="w-8 h-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">邮件发送成功！</h1>
          <p className="text-gray-600 mb-4">正在跳转到验证等待页面...</p>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100 px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">验证邮件发送失败</h1>
          <p className="text-gray-600">
            向 <strong className="text-gray-900">{user.email}</strong> 发送验证邮件时出现问题
          </p>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-yellow-800 mb-2">可能的原因</h3>
          <ul className="text-sm text-yellow-700 text-left space-y-1">
            <li>• 邮件服务暂时不可用</li>
            <li>• 邮箱地址可能存在问题</li>
            <li>• 网络连接不稳定</li>
          </ul>
        </div>

        <div className="space-y-4">
          <button
            onClick={handleRetryEmail}
            disabled={isRetrying}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshIcon className={`w-4 h-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? '重新发送中...' : '重新发送验证邮件'}
          </button>

          <button
            onClick={() => router.push('/register')}
            className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            返回注册页面
          </button>
        </div>

        <div className="mt-6 text-xs text-gray-500">
          <p>如果问题持续存在，请
            <a href="/support" className="text-blue-600 hover:text-blue-500 ml-1">联系客服</a>
            或尝试使用其他邮箱地址重新注册
          </p>
        </div>
      </div>
    </div>
  );
}
```

### 4. 邮箱验证处理页面 (`/verify-email`)

#### 核心功能
- 处理邮件中的验证链接
- 调用后端验证接口
- 根据验证结果跳转到相应页面

#### 实现代码
```typescript
// pages/verify-email.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { LoadingSpinner } from '../components/LoadingSpinner';

export default function VerifyEmailPage() {
  const router = useRouter();
  const { token } = router.query;
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    if (!token) return;

    const verifyEmail = async () => {
      try {
        // 调用 Better Auth 的验证接口
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/verify-email?token=${token}`, {
          method: 'GET',
        });

        if (response.ok) {
          // 验证成功，跳转到成功页面
          router.push('/verify-success');
        } else {
          // 验证失败，跳转到失败页面
          router.push('/verify-failed');
        }
      } catch (error) {
        console.error('邮箱验证失败:', error);
        router.push('/verify-failed');
      } finally {
        setIsVerifying(false);
      }
    };

    verifyEmail();
  }, [token, router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        <LoadingSpinner size="large" />
        <h1 className="text-xl font-semibold text-gray-900 mt-4">正在验证您的邮箱...</h1>
        <p className="text-gray-600 mt-2">请稍候，这只需要几秒钟</p>
      </div>
    </div>
  );
}
```

---

## 🚨 错误处理策略

### 1. API错误处理

#### 错误类型分类
```typescript
// types/errors.ts
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INVITE_CODE = 'INVALID_INVITE_CODE',
  USER_EXISTS = 'USER_EXISTS',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EMAIL_SEND_FAILED = 'EMAIL_SEND_FAILED',
}

export interface ApiError {
  error: ErrorType;
  message: string;
  details?: any;
}
```

```typescript
// hooks/useErrorHandler.ts
import { useState } from 'react';
import { ApiError, ErrorType } from '../types/errors';

export function useErrorHandler() {
  const [error, setError] = useState<string | null>(null);

  const handleError = (error: unknown) => {
    if (error instanceof Error) {
      // 网络错误
      if (error.message.includes('fetch')) {
        setError('网络连接失败，请检查网络后重试');
        return;
      }

      // API错误
      try {
        const apiError: ApiError = JSON.parse(error.message);
        switch (apiError.error) {
          case ErrorType.VALIDATION_ERROR:
            setError('输入信息有误，请检查后重试');
            break;
          case ErrorType.INVALID_INVITE_CODE:
            setError('邀请码无效或已使用，请检查邀请码');
            break;
          case ErrorType.USER_EXISTS:
            setError('该邮箱已注册，请直接登录或使用其他邮箱');
            break;
          case ErrorType.EMAIL_SEND_FAILED:
            setError('验证邮件发送失败，请稍后重试');
            break;
          default:
            setError(apiError.message || '操作失败，请重试');
        }
      } catch {
        setError(error.message);
      }
    } else {
      setError('未知错误，请重试');
    }
  };

  const clearError = () => setError(null);

  return { error, handleError, clearError };
}
```
## 🔄 状态同步和数据流

### 1. 页面间状态传递
```typescript
// utils/navigation.ts
export class NavigationManager {
  static navigateToVerificationPending(user: User, emailStatus: EmailVerificationStatus) {
    // 保存状态到 localStorage 或 sessionStorage
    sessionStorage.setItem('verification-context', JSON.stringify({
      user,
      emailStatus,
      timestamp: Date.now(),
    }));

    window.location.href = '/email-verification-pending';
  }

  static getVerificationContext(): { user: User; emailStatus: EmailVerificationStatus } | null {
    try {
      const context = sessionStorage.getItem('verification-context');
      if (!context) return null;

      const parsed = JSON.parse(context);

      // 检查是否过期（10分钟）
      if (Date.now() - parsed.timestamp > 10 * 60 * 1000) {
        sessionStorage.removeItem('verification-context');
        return null;
      }

      return parsed;
    } catch {
      return null;
    }
  }
}
```

### 2. 实时状态更新
```typescript
// hooks/useEmailVerificationStatus.ts
import { useState, useEffect } from 'react';
import { AuthService } from '../services/auth';

export function useEmailVerificationStatus(email: string, interval: number = 30000) {
  const [status, setStatus] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    if (!email) return;

    const checkStatus = async () => {
      try {
        setIsChecking(true);
        const response = await AuthService.checkEmailVerification(email);
        setStatus(response.data);
      } catch (error) {
        console.error('检查验证状态失败:', error);
      } finally {
        setIsChecking(false);
      }
    };

    // 立即检查一次
    checkStatus();

    // 定期检查
    const intervalId = setInterval(checkStatus, interval);

    return () => clearInterval(intervalId);
  }, [email, interval]);

  return { status, isChecking };
}
```

---

### 3. 性能优化
```typescript
// 代码分割和懒加载
const EmailVerificationPending = dynamic(() => import('../pages/email-verification-pending'), {
  loading: () => <LoadingSpinner />,
});

// 预加载关键资源
export function preloadVerificationPages() {
  import('../pages/email-verification-pending');
  import('../pages/email-verification-failed');
  import('../pages/verify-success');
}
```

---

## ✅ 验收标准

### 1. 功能验收
- [ ] 注册表单提交成功，显示正确的状态信息
- [ ] 邮件发送成功时跳转到等待页面
- [ ] 邮件发送失败时跳转到失败页面并提供重试选项
- [ ] 邮箱验证链接处理正确
- [ ] 所有错误情况都有适当的用户反馈

### 2. 用户体验验收
- [ ] 每个步骤都有清晰的状态指示
- [ ] 加载状态和进度反馈及时
- [ ] 错误信息友好且可操作
- [ ] 移动端体验良好

### 3. 技术验收
- [ ] TypeScript 类型检查通过
- [ ] 所有API调用都有错误处理
- [ ] 状态管理正确且一致
- [ ] 性能指标达标（首屏加载 < 2s）

---

**文档版本**: v1.0
**创建时间**: 2025-01-31
**适用技术栈**: React/Next.js + TypeScript + Tailwind CSS
**后端接口版本**: 与 2025-01-31 修复后的接口兼容
