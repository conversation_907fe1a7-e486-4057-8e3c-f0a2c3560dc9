/**
 * 聊天容器组件 - 统一的chat界面容器
 * 基于新的chat-store.ts架构重构
 */

'use client'

import { cn } from '@/lib/utils'
import { useCallback, useEffect, useRef } from 'react'

// 使用新的统一hooks
import { useAutoConnect, useChat } from '@/hooks/use-chat'

// 子组件
import { AIChatInput } from './ai-chat-input'
import { ConnectionStatus } from './connection-status'
import { MessageList } from './message-list'
import VirtualizedMessageList, { VirtualizedMessageListRef } from './virtualized-message-list'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface ChatContainerProps {
  /** 群聊ID */
  groupChatId: string
  /** 用户ID */
  userId: string
  /** 组织ID */
  organizationId: string

  /** 聊天界面显示配置 */
  appearance?: {
    showAvatar?: boolean
    showTimestamp?: boolean
    enableCopy?: boolean
  }

  /** 功能开关 */
  features?: {
    autoConnect?: boolean
    enableFileUpload?: boolean
    enableVirtualScroll?: boolean // 是否启用虚拟滚动
  }

  /** 自定义样式 */
  className?: string

  /** 事件回调 */
  onError?: (error: Error) => void
}

// ============================================================================
// 主要组件
// ============================================================================

export const ChatContainer: React.FC<ChatContainerProps> = ({
  groupChatId,
  userId,
  organizationId,
  appearance = {},
  features = {},
  className,
  onError = undefined,
}) => {
  const scrollRef = useRef<HTMLDivElement>(null)
  const virtualizedListRef = useRef<VirtualizedMessageListRef>(null)

  // 使用统一的chat hook
  const {
    connectionStatus,
    isConnected,
    isLoading,
    error,
    messages,
    inputText,
    isSending,
    sendMessage,
    setInputText,
  } = useChat()

  // 自动连接
  useAutoConnect(groupChatId, userId, organizationId, features.autoConnect ?? true)

  // 错误处理
  useEffect(() => {
    if (error && onError) {
      onError(new Error(error))
    }
  }, [error, onError])

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (features.enableVirtualScroll && virtualizedListRef.current) {
      // 虚拟滚动模式
      virtualizedListRef.current.scrollToBottom()
    } else if (scrollRef.current) {
      // 传统滚动模式
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [features.enableVirtualScroll])

  // 当有新消息时滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [messages.length, scrollToBottom])

  // 发送消息处理
  const handleSendMessage = useCallback(
    async (message: string) => {
      try {
        await sendMessage(message)
        setInputText('')
      } catch (err) {
        const error = err as Error
        if (onError) {
          onError(error)
        }
      }
    },
    [sendMessage, setInputText, onError]
  )

  // 输入文本变化处理
  const handleInputChange = useCallback(
    (text: string) => {
      setInputText(text)
    },
    [setInputText]
  )

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* 连接状态显示 */}
      <ConnectionStatus status={connectionStatus} error={error} className="flex-shrink-0" />

      {/* 消息列表区域 */}
      {features.enableVirtualScroll ? (
        // 虚拟滚动模式
        <div className="flex-1 relative">
          <VirtualizedMessageList
            ref={virtualizedListRef}
            messages={messages}
            showAvatar={appearance.showAvatar ?? true}
            showTimestamp={appearance.showTimestamp ?? true}
            enableCopy={appearance.enableCopy ?? true}
            isLoading={isLoading}
            itemHeight={120} // 可配置的消息项高度
          />
        </div>
      ) : (
        // 传统滚动模式
        <div ref={scrollRef} className="flex-1 overflow-y-auto px-4 py-2">
          <MessageList
            messages={messages}
            showAvatar={appearance.showAvatar ?? true}
            showTimestamp={appearance.showTimestamp ?? true}
            enableCopy={appearance.enableCopy ?? true}
            isLoading={isLoading}
          />
        </div>
      )}

      {/* 消息输入区域 */}
      <AIChatInput
        value={inputText}
        onChange={handleInputChange}
        onSendMessage={handleSendMessage}
        disabled={!isConnected || isSending}
        loading={isSending}
        placeholder="输入消息..."
        enableFileUpload={features.enableFileUpload ?? false}
        showAttachButton={features.enableFileUpload ?? false}
        showVoiceButton={false} // 聊天容器中不显示语音按钮
      />
    </div>
  )
}

export default ChatContainer
