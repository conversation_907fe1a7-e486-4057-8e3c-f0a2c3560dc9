#!/usr/bin/env node

/**
 * 简化Chat系统集成测试脚本
 * 
 * 功能：
 * 1. 测试前后端Socket.IO连接
 * 2. 验证认证参数传递
 * 3. 测试消息发送接收
 * 4. 检查错误处理
 */

const { io } = require('socket.io-client');

// 测试配置
const TEST_CONFIG = {
  socketUrl: 'ws://localhost:8000',
  timeout: 10000,
  testData: {
    userId: 'integration-test-user',
    groupChatId: 'integration-test-group',
    organizationId: 'integration-test-org'
  }
};

class ChatIntegrationTester {
  constructor() {
    this.socket = null;
    this.testResults = [];
    this.timeouts = [];
  }

  // 记录测试结果
  logResult(testName, success, message, data = null) {
    const result = {
      test: testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    if (data && typeof data === 'object') {
      console.log(`   Data:`, JSON.stringify(data, null, 2));
    }
  }

  // 创建超时Promise
  createTimeout(ms, name) {
    return new Promise((_, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`${name} timeout after ${ms}ms`));
      }, ms);
      this.timeouts.push(timeoutId);
    });
  }

  // 清理所有超时
  clearTimeouts() {
    this.timeouts.forEach(clearTimeout);
    this.timeouts = [];
  }

  // 测试1: Socket连接
  async testConnection() {
    return new Promise((resolve) => {
      const testName = 'Socket连接测试';
      
      try {
        this.socket = io(TEST_CONFIG.socketUrl, {
          auth: TEST_CONFIG.testData,
          timeout: TEST_CONFIG.timeout,
          transports: ['websocket']
        });

        this.socket.on('connect', () => {
          this.logResult(testName, true, '成功建立Socket连接');
          resolve(true);
        });

        this.socket.on('connect_error', (error) => {
          this.logResult(testName, false, '连接失败', { error: error.message });
          resolve(false);
        });

        // 设置超时
        setTimeout(() => {
          this.logResult(testName, false, '连接超时');
          resolve(false);
        }, TEST_CONFIG.timeout);

      } catch (error) {
        this.logResult(testName, false, '连接异常', { error: error.message });
        resolve(false);
      }
    });
  }

  // 测试2: 认证参数验证
  async testAuthentication() {
    return new Promise((resolve) => {
      const testName = '认证参数测试';
      
      if (!this.socket || !this.socket.connected) {
        this.logResult(testName, false, 'Socket未连接');
        resolve(false);
        return;
      }

      // 监听连接确认消息
      this.socket.on('message', (data) => {
        if (data.status === 'connected') {
          const isValid = data.user_id === TEST_CONFIG.testData.userId &&
                          data.room === TEST_CONFIG.testData.groupChatId;
          
          if (isValid) {
            this.logResult(testName, true, '认证参数验证成功', data);
          } else {
            this.logResult(testName, false, '认证参数不匹配', { 
              expected: TEST_CONFIG.testData,
              received: data 
            });
          }
          resolve(isValid);
        }
      });

      // 设置超时
      setTimeout(() => {
        this.logResult(testName, false, '认证验证超时');
        resolve(false);
      }, 5000);
    });
  }

  // 测试3: 消息发送
  async testMessageSending() {
    return new Promise((resolve) => {
      const testName = '消息发送测试';
      const testMessage = '这是一个集成测试消息';
      
      if (!this.socket || !this.socket.connected) {
        this.logResult(testName, false, 'Socket未连接');
        resolve(false);
        return;
      }

      try {
        // 发送用户消息
        this.socket.emit('user_message', {
          message: testMessage,
          session_id: TEST_CONFIG.testData.groupChatId,
          payload: {
            type: 'user_message',
            content: testMessage
          }
        });

        this.logResult(testName, true, '消息发送成功', { message: testMessage });
        resolve(true);

      } catch (error) {
        this.logResult(testName, false, '消息发送失败', { error: error.message });
        resolve(false);
      }
    });
  }

  // 测试4: 事件接收
  async testEventReceiving() {
    return new Promise((resolve) => {
      const testName = '事件接收测试';
      const receivedEvents = [];
      let timeout;

      // 监听各种后端事件
      const eventHandlers = {
        'processing_status': (data) => receivedEvents.push({ type: 'processing_status', data }),
        'analysis_result': (data) => receivedEvents.push({ type: 'analysis_result', data }),
        'interaction_required': (data) => receivedEvents.push({ type: 'interaction_required', data }),
        'final_result': (data) => receivedEvents.push({ type: 'final_result', data }),
        'error': (data) => receivedEvents.push({ type: 'error', data }),
        'chat_response': (data) => receivedEvents.push({ type: 'chat_response', data })
      };

      // 注册事件监听器
      Object.keys(eventHandlers).forEach(eventName => {
        this.socket.on(eventName, eventHandlers[eventName]);
      });

      // 发送测试消息触发后端处理
      this.socket.emit('user_message', {
        message: '测试事件接收',
        session_id: TEST_CONFIG.testData.groupChatId
      });

      // 等待事件接收
      timeout = setTimeout(() => {
        // 清理事件监听器
        Object.keys(eventHandlers).forEach(eventName => {
          this.socket.off(eventName, eventHandlers[eventName]);
        });

        if (receivedEvents.length > 0) {
          this.logResult(testName, true, `成功接收${receivedEvents.length}个事件`, { events: receivedEvents });
          resolve(true);
        } else {
          this.logResult(testName, false, '未接收到任何后端事件');
          resolve(false);
        }
      }, 8000);
    });
  }

  // 测试5: 错误处理
  async testErrorHandling() {
    return new Promise((resolve) => {
      const testName = '错误处理测试';
      
      if (!this.socket || !this.socket.connected) {
        this.logResult(testName, false, 'Socket未连接');
        resolve(false);
        return;
      }

      // 监听错误事件
      this.socket.on('error', (data) => {
        this.logResult(testName, true, '成功接收错误事件', data);
        resolve(true);
      });

      // 发送无效消息格式
      try {
        this.socket.emit('user_message', {
          // 缺少必需的message字段
          invalid_field: 'invalid data'
        });

        // 如果没有接收到错误事件，5秒后认为测试通过（可选）
        setTimeout(() => {
          this.logResult(testName, true, '错误处理测试完成（无错误产生）');
          resolve(true);
        }, 3000);

      } catch (error) {
        this.logResult(testName, true, '捕获到客户端错误', { error: error.message });
        resolve(true);
      }
    });
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始运行简化Chat系统集成测试...\n');
    console.log(`测试配置:`);
    console.log(`- Socket URL: ${TEST_CONFIG.socketUrl}`);
    console.log(`- 测试数据:`, TEST_CONFIG.testData);
    console.log(`- 超时时间: ${TEST_CONFIG.timeout}ms\n`);

    const tests = [
      { name: '连接测试', fn: () => this.testConnection() },
      { name: '认证测试', fn: () => this.testAuthentication() },
      { name: '消息发送测试', fn: () => this.testMessageSending() },
      { name: '事件接收测试', fn: () => this.testEventReceiving() },
      { name: '错误处理测试', fn: () => this.testErrorHandling() }
    ];

    let passedTests = 0;
    
    for (const test of tests) {
      console.log(`\n📋 执行 ${test.name}...`);
      try {
        const result = await test.fn();
        if (result) passedTests++;
      } catch (error) {
        this.logResult(test.name, false, '测试执行异常', { error: error.message });
      }
    }

    // 清理资源
    this.cleanup();

    // 输出测试总结
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试总结');
    console.log('='.repeat(50));
    console.log(`总测试数: ${tests.length}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${tests.length - passedTests}`);
    console.log(`成功率: ${((passedTests / tests.length) * 100).toFixed(1)}%`);
    
    if (passedTests === tests.length) {
      console.log('\n🎉 所有测试通过！简化Chat系统集成正常。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查系统配置。');
    }

    // 输出详细结果
    console.log('\n📝 详细测试结果:');
    this.testResults.forEach((result, index) => {
      console.log(`${index + 1}. ${result.test}: ${result.success ? '通过' : '失败'} - ${result.message}`);
    });

    return passedTests === tests.length;
  }

  // 清理资源
  cleanup() {
    this.clearTimeouts();
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}

// 运行测试
async function main() {
  const tester = new ChatIntegrationTester();
  
  try {
    const success = await tester.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 测试运行失败:', error);
    tester.cleanup();
    process.exit(1);
  }
}

// 处理进程终止
process.on('SIGINT', () => {
  console.log('\n⚠️  测试被中断');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  测试被终止');
  process.exit(1);
});

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = ChatIntegrationTester;