/**
 * 邮件发送失败页面 - 客户端组件
 * 处理重试逻辑和用户交互
 */

'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { RefreshCw, CheckCircle, ExternalLink, ArrowLeft } from 'lucide-react'
import { useEmailVerification } from '@/hooks/use-email-verification'
import { cn } from '@/lib/utils'

export function EmailVerificationFailedClient() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const t = useTranslations('auth.verification')
  
  // 从URL参数获取邮箱地址和错误信息
  const email = searchParams.get('email') || ''
  const errorReason = searchParams.get('error') || ''
  
  const [retrySuccess, setRetrySuccess] = useState(false)

  // 如果没有邮箱参数，重定向到注册页面
  useEffect(() => {
    if (!email) {
      router.push('/register')
    }
  }, [email, router])

  // 使用邮箱验证Hook
  const {
    isResending,
    error,
    resendEmail,
    clearError
  } = useEmailVerification({ email })

  const handleRetryEmail = async () => {
    clearError()
    try {
      await resendEmail()
      setRetrySuccess(true)
      
      // 3秒后跳转到等待页面
      setTimeout(() => {
        router.push(`/email-verification-pending?email=${encodeURIComponent(email)}`)
      }, 3000)
    } catch (error) {
      // 错误已经在Hook中处理
    }
  }

  const handleBackToRegister = () => {
    router.push('/register')
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  if (!email) {
    return null // 等待重定向
  }

  // 重试成功状态
  if (retrySuccess) {
    return (
      <div className="text-center space-y-4">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">邮件发送成功！</h3>
          <p className="text-gray-600 mb-4">正在跳转到验证等待页面...</p>
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 邮箱地址显示 */}
      <div className="text-center">
        <p className="text-gray-600">
          向以下邮箱发送验证邮件时失败
        </p>
        <p className="font-semibold text-gray-900 break-all">
          {email}
        </p>
      </div>

      {/* 错误原因显示 */}
      {errorReason && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm font-medium">错误详情：</p>
          <p className="text-red-700 text-sm mt-1">{errorReason}</p>
        </div>
      )}

      {/* 可能的原因和解决方案 */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="font-semibold text-yellow-800 mb-2">可能的原因</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>邮件服务暂时不可用</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>邮箱地址可能存在问题</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>网络连接不稳定</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>邮件服务器配置问题</span>
          </li>
        </ul>
      </div>

      {/* 当前错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">{error}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearError}
            className="mt-2 text-red-600 hover:text-red-700"
          >
            关闭
          </Button>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="space-y-3">
        <Button
          onClick={handleRetryEmail}
          disabled={isResending}
          className="w-full"
        >
          <RefreshCw className={cn(
            "w-4 h-4 mr-2",
            isResending && "animate-spin"
          )} />
          {isResending ? '重新发送中...' : '重新发送验证邮件'}
        </Button>

        <div className="grid grid-cols-2 gap-3">
          <Button
            onClick={handleBackToRegister}
            variant="outline"
            className="w-full"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回注册
          </Button>

          <Button
            onClick={handleBackToLogin}
            variant="ghost"
            className="w-full"
          >
            返回登录
          </Button>
        </div>
      </div>

      {/* 帮助信息 */}
      <div className="text-center text-xs text-gray-500 space-y-2">
        <p>如果问题持续存在，请尝试：</p>
        <ul className="text-left space-y-1 max-w-xs mx-auto">
          <li>• 检查邮箱地址是否正确</li>
          <li>• 使用其他邮箱地址重新注册</li>
          <li>• 稍后再试</li>
        </ul>
        <p className="pt-2">
          需要帮助？
          <Button
            variant="link"
            size="sm"
            className="text-xs p-0 h-auto text-blue-600 hover:text-blue-500 ml-1"
            onClick={() => window.open('/support', '_blank')}
          >
            联系客服
            <ExternalLink className="w-3 h-3 ml-1" />
          </Button>
        </p>
      </div>
    </div>
  )
}
