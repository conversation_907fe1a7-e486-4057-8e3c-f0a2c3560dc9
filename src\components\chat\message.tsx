/**
 * 统一消息组件 - 支持所有消息类型的显示和交互
 * 基于BaseWebSocketMessage标准设计
 */

'use client'

import { memo, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'
import type { 
  BaseWebSocketMessage, 
  StreamingPayload, 
  CheckpointPayload, 
  ReportPayload, 
  ErrorPayload 
} from '@/types/websocket-event-type'
import { 
  isStreamingPayload, 
  isCheckpointPayload, 
  isReportPayload, 
  isErrorPayload 
} from '@/types/websocket-event-type'

// UI组件
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { 
  User, 
  <PERSON><PERSON>, 
  Co<PERSON>, 
  Check, 
  AlertCircle, 
  MessageSquare, 
  Clock,
  CheckCircle2,
  XCircle
} from 'lucide-react'
import { useChatMessages } from '@/hooks/use-chat'

// ============================================================================
// 消息组件接口
// ============================================================================

export interface MessageProps {
  /** 消息数据 */
  message: BaseWebSocketMessage
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 是否显示时间戳 */
  showTimestamp?: boolean
  /** 是否启用复制功能 */
  enableCopy?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 复制回调 */
  onCopy?: ((content: string) => void) | undefined
}

// ============================================================================
// 主消息组件
// ============================================================================

const Message = memo<MessageProps>(({
  message,
  showAvatar = true,
  showTimestamp = true,
  enableCopy = true,
  className,
  onCopy = undefined,
}) => {
  const [copiedId, setCopiedId] = useState<string | null>(null)
  const { sendInteraction } = useChatMessages()

  // 判断消息角色（用户消息 vs AI消息）
  const isUserMessage = !!message.userId && message.payload.type === 'streaming' && 
    isStreamingPayload(message.payload) && message.payload.isComplete
  
  // 复制功能
  const handleCopy = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedId(message.id)
      if (onCopy) {
        onCopy(content)
      }
      setTimeout(() => setCopiedId(null), 2000)
    } catch (err) {
      // Handle copy error silently
    }
  }, [message.id, onCopy])

  // 处理交互响应
  const handleInteraction = useCallback(async (step: string, selection: string) => {
    try {
      await sendInteraction(step, selection)
    } catch (err) {
      // Handle interaction error silently
    }
  }, [sendInteraction])

  // 渲染消息状态指示器
  const renderStatusIndicator = () => {
    if (!message.status) return null

    const statusConfig = {
      sending: { icon: Clock, color: 'text-yellow-500', text: '发送中...' },
      sent: { icon: Check, color: 'text-green-500', text: '已发送' },
      delivered: { icon: CheckCircle2, color: 'text-green-600', text: '已送达' },
      failed: { icon: XCircle, color: 'text-red-500', text: '发送失败' },
    }

    const config = statusConfig[message.status]
    if (!config) return null

    const Icon = config.icon
    return (
      <div className={cn('flex items-center gap-1 text-xs', config.color)}>
        <Icon className="h-3 w-3" />
        <span>{config.text}</span>
      </div>
    )
  }

  // 渲染消息时间戳
  const renderTimestamp = () => {
    if (!showTimestamp) return null
    
    return (
      <span className="text-xs text-muted-foreground">
        {new Date(message.timestamp).toLocaleTimeString()}
      </span>
    )
  }

  // 渲染用户头像
  const renderAvatar = (isUser: boolean) => {
    if (!showAvatar) return null

    return (
      <Avatar className="h-8 w-8">
        <AvatarFallback>
          {isUser ? (
            <User className="h-4 w-4" />
          ) : (
            <Bot className="h-4 w-4" />
          )}
        </AvatarFallback>
      </Avatar>
    )
  }

  // 渲染消息内容
  const renderMessageContent = () => {
    const { payload } = message

    // 流式消息 (用户消息和AI流式响应)
    if (isStreamingPayload(payload)) {
      return (
        <StreamingMessageContent 
          payload={payload}
          enableCopy={enableCopy}
          onCopy={handleCopy}
          isCopied={copiedId === message.id}
        />
      )
    }

    // 检查点消息 (交互式表单)
    if (isCheckpointPayload(payload)) {
      return (
        <CheckpointMessageContent 
          payload={payload}
          messageId={message.id}
          onInteraction={handleInteraction}
        />
      )
    }

    // 报告消息 (最终结果)
    if (isReportPayload(payload)) {
      return (
        <ReportMessageContent 
          payload={payload}
          enableCopy={enableCopy}
          onCopy={handleCopy}
          isCopied={copiedId === message.id}
        />
      )
    }

    // 错误消息
    if (isErrorPayload(payload)) {
      return (
        <ErrorMessageContent payload={payload} />
      )
    }

    return <div className="text-muted-foreground">未知消息类型</div>
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex gap-3 p-4',
        isUserMessage ? 'justify-end' : 'justify-start',
        className
      )}
    >
      {/* 左侧头像（AI消息） */}
      {!isUserMessage && renderAvatar(false)}
      
      {/* 消息内容区域 */}
      <div className={cn(
        'flex flex-col gap-2 max-w-[70%]',
        isUserMessage ? 'items-end' : 'items-start'
      )}>
        <div className={cn(
          'rounded-lg px-4 py-2',
          isUserMessage 
            ? 'bg-primary text-primary-foreground' 
            : 'bg-muted text-foreground'
        )}>
          {renderMessageContent()}
        </div>
        
        {/* 消息元信息 */}
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {renderTimestamp()}
          {renderStatusIndicator()}
        </div>
      </div>

      {/* 右侧头像（用户消息） */}
      {isUserMessage && renderAvatar(true)}
    </motion.div>
  )
})

Message.displayName = 'Message'

// ============================================================================
// 消息内容组件
// ============================================================================

interface StreamingMessageContentProps {
  payload: StreamingPayload
  enableCopy: boolean
  onCopy: (content: string) => void
  isCopied: boolean
}

const StreamingMessageContent = memo<StreamingMessageContentProps>(({ 
  payload, 
  enableCopy, 
  onCopy, 
  isCopied 
}) => {
  return (
    <div className="relative group">
      <div className="whitespace-pre-wrap break-words">
        {payload.delta}
      </div>
      
      {/* 流式状态指示器 */}
      {!payload.isComplete && (
        <motion.div
          animate={{ opacity: [0.4, 1, 0.4] }}
          transition={{ duration: 1.5, repeat: Infinity }}
          className="inline-block w-2 h-4 bg-current ml-1"
        />
      )}
      
      {/* 复制按钮 */}
      {enableCopy && payload.isComplete && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={() => onCopy(payload.delta)}
        >
          {isCopied ? (
            <Check className="h-3 w-3 text-green-500" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      )}
    </div>
  )
})

StreamingMessageContent.displayName = 'StreamingMessageContent'

interface CheckpointMessageContentProps {
  payload: CheckpointPayload
  messageId: string
  onInteraction: (step: string, selection: string) => void
}

const CheckpointMessageContent = memo<CheckpointMessageContentProps>(({ 
  payload, 
  messageId, 
  onInteraction 
}) => {
  const [selectedValue, setSelectedValue] = useState<string>('')

  const handleSubmit = useCallback(() => {
    if (selectedValue) {
      onInteraction('user_selection', selectedValue)
    }
  }, [selectedValue, onInteraction])

  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span className="font-medium">请选择一个选项</span>
          </div>
          
          <RadioGroup value={selectedValue} onValueChange={setSelectedValue}>
            {payload.fields.map((field) => (
              <div key={field.id} className="flex items-center space-x-2">
                <RadioGroupItem value={field.id} id={field.id} />
                <Label htmlFor={field.id}>{field.label}</Label>
              </div>
            ))}
          </RadioGroup>
          
          <Button 
            onClick={handleSubmit} 
            disabled={!selectedValue}
            className="w-full"
          >
            提交选择
          </Button>
        </div>
      </CardContent>
    </Card>
  )
})

CheckpointMessageContent.displayName = 'CheckpointMessageContent'

interface ReportMessageContentProps {
  payload: ReportPayload
  enableCopy: boolean
  onCopy: (content: string) => void
  isCopied: boolean
}

const ReportMessageContent = memo<ReportMessageContentProps>(({ 
  payload, 
  enableCopy, 
  onCopy, 
  isCopied 
}) => {
  return (
    <div className="relative group">
      <div className="space-y-2">
        <Badge variant="secondary">分析报告</Badge>
        <div 
          className="prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: payload.content }}
        />
      </div>
      
      {/* 复制按钮 */}
      {enableCopy && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={() => onCopy(payload.content)}
        >
          {isCopied ? (
            <Check className="h-3 w-3 text-green-500" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      )}
    </div>
  )
})

ReportMessageContent.displayName = 'ReportMessageContent'

interface ErrorMessageContentProps {
  payload: ErrorPayload
}

const ErrorMessageContent = memo<ErrorMessageContentProps>(({ payload }) => {
  return (
    <div className="flex items-start gap-2 text-destructive">
      <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
      <div>
        <div className="font-medium">出现错误</div>
        <div className="text-sm">{payload.message}</div>
      </div>
    </div>
  )
})

ErrorMessageContent.displayName = 'ErrorMessageContent'

export default Message