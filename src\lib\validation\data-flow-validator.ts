/**
 * 数据流验证器
 * 确保从Next.js Socket.IO API路由到UI的完整数据管道正常工作
 */

import { useChatStore } from '@/stores/chat-store'
import type { BaseWebSocketMessage, MessagePayload } from '@/types/websocket-event-type'

export interface ValidationResult {
  test: string
  passed: boolean
  message: string
  details?: any
  duration?: number
}

export interface ValidationReport {
  overall: boolean
  results: ValidationResult[]
  summary: {
    total: number
    passed: number
    failed: number
    duration: number
  }
}

export class DataFlowValidator {
  private results: ValidationResult[] = []
  private startTime: number = 0

  // 运行完整的数据流验证
  async runFullValidation(): Promise<ValidationReport> {
    this.results = []
    this.startTime = Date.now()

    console.log('🔍 开始数据流验证...')

    // 1. 状态管理验证
    await this.validateStateManagement()

    // 2. 组件初始化验证
    await this.validateComponentInitialization()

    return this.generateReport()
  }

  // 验证组件初始化
  private async validateComponentInitialization(): Promise<void> {
    try {
      const chatStore = useChatStore.getState()

      // 测试WebSocket连接功能存在
      this.addResult('WebSocket连接', !!chatStore.connect, 'WebSocket连接功能可用')

      // 测试基础功能
      this.addResult(
        '基础功能',
        typeof chatStore.createSession === 'function' &&
          typeof chatStore.sendMessage === 'function',
        '基础聊天功能可用'
      )

      console.log('✅ Socket.IO现在使用Next.js API路由，无需独立Mock服务器')
    } catch (error) {
      this.addResult(
        '组件初始化验证',
        false,
        `组件初始化验证失败: ${error instanceof Error ? error.message : '未知错误'}`
      )
    }
  }

  // 验证状态管理
  private async validateStateManagement(): Promise<void> {
    try {
      const chatStore = useChatStore.getState()

      // 测试状态初始化
      this.addResult(
        '状态初始化',
        Object.keys(chatStore.sessions).length === 0 && chatStore.inputText === '',
        '聊天状态初始化正确'
      )

      // 测试会话管理
      const sessionId = chatStore.createSession('test-group', 'test-user', 'test-org')
      this.addResult(
        '状态-会话创建',
        Object.keys(chatStore.sessions).length === 1 && !!sessionId,
        '会话状态管理正确'
      )

      // 测试消息管理
      const testMessage: BaseWebSocketMessage = {
        id: 'test-msg-1',
        groupChatId: 'test-group',
        sessionId: sessionId,
        userId: 'test-user',
        organizationId: 'test-org',
        payload: {
          type: 'streaming',
          delta: '测试消息',
          isComplete: true,
        },
        timestamp: new Date().toISOString(),
        status: 'sent',
      }
      chatStore.addMessage(testMessage)

      const currentSession = chatStore.sessions[sessionId]
      this.addResult('状态-消息管理', currentSession?.messages.length === 1, '消息状态管理正确')

      // 测试用户输入
      chatStore.setInputText('测试输入')
      this.addResult(
        '状态-用户输入',
        chatStore.inputText === '测试输入',
        '用户输入状态管理正确'
      )

      // 清理状态
      chatStore.reset()
      this.addResult(
        '状态清理',
        Object.keys(chatStore.sessions).length === 0 && chatStore.inputText === '',
        '状态清理功能正常'
      )
    } catch (error) {
      this.addResult(
        '状态管理验证',
        false,
        `状态管理验证失败: ${error instanceof Error ? error.message : '未知错误'}`
      )
    }
  }

  // 添加验证结果
  private addResult(test: string, passed: boolean, message: string, details?: any): void {
    const duration = Date.now() - this.startTime
    this.results.push({
      test,
      passed,
      message,
      details,
      duration,
    })

    console.log(`${passed ? '✅' : '❌'} ${test}: ${message}`)
  }

  // 生成验证报告
  private generateReport(): ValidationReport {
    const totalDuration = Date.now() - this.startTime
    const passed = this.results.filter(r => r.passed).length
    const failed = this.results.length - passed
    const overall = failed === 0

    const report: ValidationReport = {
      overall,
      results: this.results,
      summary: {
        total: this.results.length,
        passed,
        failed,
        duration: totalDuration,
      },
    }

    console.log(`\n📊 验证报告:`)
    console.log(`总体结果: ${overall ? '✅ 通过' : '❌ 失败'}`)
    console.log(`总测试数: ${this.results.length}`)
    console.log(`通过: ${passed}`)
    console.log(`失败: ${failed}`)
    console.log(`耗时: ${totalDuration}ms`)

    return report
  }

  // 运行单项测试
  async runSingleTest(testName: string): Promise<ValidationResult> {
    this.results = []
    this.startTime = Date.now()

    switch (testName) {
      case 'state-management':
        await this.validateStateManagement()
        break
      case 'component-initialization':
        await this.validateComponentInitialization()
        break
      default:
        this.addResult('未知测试', false, `未知的测试名称: ${testName}`)
    }

    return (
      this.results[0] || {
        test: testName,
        passed: false,
        message: '测试未执行',
      }
    )
  }

  // 获取验证统计
  getValidationStats(): {
    testsAvailable: string[]
    lastRunSummary: ValidationReport['summary'] | undefined
  } {
    const summary =
      this.results.length > 0
        ? {
            total: this.results.length,
            passed: this.results.filter(r => r.passed).length,
            failed: this.results.filter(r => !r.passed).length,
            duration: Date.now() - this.startTime,
          }
        : undefined

    return {
      testsAvailable: ['state-management', 'component-initialization'],
      lastRunSummary: summary,
    }
  }
}

// 导出单例实例
export const dataFlowValidator = new DataFlowValidator()

// 便捷函数
export async function validateDataFlow(): Promise<ValidationReport> {
  return await dataFlowValidator.runFullValidation()
}

export async function validateSingleComponent(testName: string): Promise<ValidationResult> {
  return await dataFlowValidator.runSingleTest(testName)
}
